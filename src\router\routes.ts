import type { RouteRecordRaw } from "vue-router";

export const constantRoute: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "Layout",
    component: () => import("@/views/home/<USER>"),
  },
  {
    path: "/trainingExamination",
    name: "TrainingExamination",
    component: () => import("@/views/TrainingExamination/index.vue"),
  },
  {
    path: "/personnel-qualification-preview",
    name: "PersonnelQualificationPreview",
    component: () => import("@/views/personnelQualificationPreview/index.vue"),
  },
  {
    path: "/personnel-qualification-entry",
    name: "PersonnelQualificationEntry",
    component: () => import("@/views/personnelQualificationEntry/index.vue"),
  },
  {
    path: "/demo",
    name: "Demo",
    component: () => import("@/views/demo/index.vue"),
  },
];
