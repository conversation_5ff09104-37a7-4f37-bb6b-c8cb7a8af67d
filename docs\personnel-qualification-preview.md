# 人员资质批量预览功能

## 功能概述

基于Figma高保真原型图实现的人员资质录入页面批量预览功能，支持通过iframe打开时在URL传递表格数据。

## 功能特性

### 1. 批量预览功能
- 人员资质录入页面新增批量预览按钮
- 点击后打开弹窗，展示三栏布局：
  - 左栏：人员选择（姓名+工种）
  - 中栏：资质类型选择（类型+上传状态）
  - 右栏：附件预览区域

### 2. 交互功能
- 点击不同人员可以切换查看
- 点击资质类型可以加载对应附件
- 支持搜索功能快速定位人员
- 支持多种附件格式预览（图片、PDF等）

### 3. 审核意见功能
- 批量预览页面新增固定文本框
- 可填写审核意见内容
- 关闭预览页面时自动填充到录入审核页面

### 4. 通用化设计
- 可修改不同的数据源复用
- 支持项目-资质类型-附件的数据结构
- 支持iframe传递数据参数

## 技术实现

### 技术栈
- Vue 3 + TypeScript
- Element Plus UI组件库
- SCSS样式预处理器
- Vue Router路由管理

### 组件结构
```
src/views/
├── personnelQualificationPreview/     # 批量预览组件
│   └── index.vue
├── personnelQualificationEntry/       # 人员资质录入页面
│   └── index.vue
└── demo/                              # 功能演示页面
    └── index.vue
```

### 路由配置
```typescript
// src/router/routes.ts
{
  path: "/personnel-qualification-preview",
  name: "PersonnelQualificationPreview",
  component: () => import("@/views/personnelQualificationPreview/index.vue"),
},
{
  path: "/personnel-qualification-entry", 
  name: "PersonnelQualificationEntry",
  component: () => import("@/views/personnelQualificationEntry/index.vue"),
},
{
  path: "/demo",
  name: "Demo", 
  component: () => import("@/views/demo/index.vue"),
}
```

## 使用方法

### 1. 基本使用
```vue
<template>
  <div>
    <el-button @click="openPreview">批量预览</el-button>
    
    <PersonnelQualificationPreview 
      ref="previewRef"
      @review-comment="handleReviewComment"
    />
  </div>
</template>

<script setup>
import PersonnelQualificationPreview from '@/views/personnelQualificationPreview/index.vue'

const previewRef = ref()

const openPreview = () => {
  previewRef.value?.openDialog({
    personnelList: [
      { id: '1', name: '张三', workType: '普通工人' },
      { id: '2', name: '李四', workType: '安装人员' }
    ],
    mode: 'batch'
  })
}

const handleReviewComment = (comment) => {
  console.log('审核意见:', comment)
}
</script>
```

### 2. iframe传递数据
```javascript
// 通过URL参数传递表格数据
const tableData = [
  { id: '1', name: '张三', workType: '普通工人' },
  { id: '2', name: '李四', workType: '安装人员' }
]

const url = `/personnel-qualification-entry?tableData=${encodeURIComponent(JSON.stringify(tableData))}`
```

### 3. 数据结构
```typescript
// 人员数据结构
interface Personnel {
  id: string
  name: string
  workType: string
}

// 资质数据结构
interface Qualification {
  id: string
  name: string
  uploaded: boolean
  required: boolean
}

// 附件数据结构
interface Attachment {
  id: string
  name: string
  url: string
  type: string
}
```

## 页面访问

### 开发环境
- 演示页面: http://localhost:5173/demo
- 录入页面: http://localhost:5173/personnel-qualification-entry
- 预览组件: http://localhost:5173/personnel-qualification-preview

### 功能演示
1. 访问 `/demo` 页面查看功能演示
2. 点击"打开批量预览"按钮测试预览功能
3. 点击"前往录入页面"查看完整的录入流程

## 响应式设计

组件支持响应式设计，在不同屏幕尺寸下都能正常使用：
- 桌面端：完整三栏布局
- 平板端：适配中等屏幕
- 移动端：堆叠布局，优化触摸操作

## 自定义配置

### 修改数据源
```javascript
// 可以通过修改以下数据结构来适配不同业务场景
const personnelList = ref([...])      // 人员列表
const qualificationsMap = ref({...})  // 资质映射
const attachmentsMap = ref({...})     // 附件映射
```

### 样式定制
```scss
// 可以通过修改SCSS变量来定制样式
.personnel-qualification-preview {
  // 自定义样式
}
```

## 注意事项

1. 确保项目已安装Element Plus依赖
2. 附件预览需要配置正确的文件服务器地址
3. iframe使用时需要注意跨域问题
4. 大量数据时建议添加虚拟滚动优化性能

## 更新日志

- v1.0.0: 初始版本，实现基础批量预览功能
- 支持人员选择、资质类型切换、附件预览
- 支持审核意见填写和传递
- 支持iframe数据传递
- 响应式设计适配多端
