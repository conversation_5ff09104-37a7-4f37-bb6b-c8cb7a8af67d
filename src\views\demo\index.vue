<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>人员资质批量预览功能演示</h1>
      <p>点击下面的按钮来测试批量预览功能</p>
    </div>

    <div class="demo-content">
      <el-card>
        <template #header>
          <span>功能演示</span>
        </template>
        
        <div class="demo-buttons">
          <el-button type="primary" size="large" @click="openBatchPreview">
            打开批量预览
          </el-button>
          
          <el-button type="success" size="large" @click="goToEntryPage">
            前往录入页面
          </el-button>
        </div>

        <div class="demo-description">
          <h3>功能说明：</h3>
          <ul>
            <li>1. 人员资质录入页面新增批量预览功能，点击后打开弹窗</li>
            <li>2. 第一栏为姓名+工种拼接，第二栏为资质类型+上传状态</li>
            <li>3. 可以点击切换不同的人，切换查看附件</li>
            <li>4. 在批量预览页面，点击人员，加载该人已有的资质类型</li>
            <li>5. 点击资质类型，加载附件预览</li>
            <li>6. 批量预览页面新增文本框，可填写审核意见</li>
            <li>7. 关闭预览页面时，将文本框内容填充到录入审核页面审核意见栏</li>
          </ul>
        </div>

        <div class="demo-features">
          <h3>技术特性：</h3>
          <ul>
            <li>✅ 支持通过iframe打开时在URL传递表格数据</li>
            <li>✅ 响应式设计，适配不同屏幕尺寸</li>
            <li>✅ 支持图片、PDF等多种附件格式预览</li>
            <li>✅ 可通用化，修改不同的数据源即可复用</li>
            <li>✅ 基于Vue 3 + TypeScript + Element Plus</li>
          </ul>
        </div>
      </el-card>

      <!-- 审核意见展示 -->
      <el-card v-if="reviewComment" class="review-result">
        <template #header>
          <span>从预览组件接收到的审核意见</span>
        </template>
        <el-alert
          :title="reviewComment"
          type="success"
          :closable="false"
          show-icon
        />
      </el-card>
    </div>

    <!-- 批量预览组件 -->
    <PersonnelQualificationPreview 
      ref="previewRef" 
      @review-comment="handleReviewComment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import PersonnelQualificationPreview from '@/views/personnelQualificationPreview/index.vue'

const router = useRouter()
const previewRef = ref()
const reviewComment = ref('')

// 打开批量预览
const openBatchPreview = () => {
  const mockData = {
    personnelList: [
      { id: '1', name: '丁春洋', workType: '普通工人' },
      { id: '2', name: '刘飞', workType: '安装人员' },
      { id: '3', name: '张三', workType: '普通工人' },
      { id: '4', name: '李四', workType: '安装人员' },
    ],
    mode: 'batch'
  }
  
  previewRef.value?.openDialog(mockData)
}

// 前往录入页面
const goToEntryPage = () => {
  router.push('/personnel-qualification-entry')
}

// 处理审核意见
const handleReviewComment = (comment: string) => {
  reviewComment.value = comment
  ElMessage.success('已接收到审核意见！')
}
</script>

<style scoped lang="scss">
.demo-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .demo-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      color: #121314;
      margin-bottom: 10px;
      font-size: 28px;
    }

    p {
      color: #909399;
      font-size: 16px;
    }
  }

  .demo-content {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .el-card {
      :deep(.el-card__header) {
        background-color: #fafafa;
        
        span {
          font-weight: 600;
          color: #121314;
          font-size: 16px;
        }
      }

      .demo-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 30px;

        .el-button {
          padding: 12px 24px;
          font-size: 16px;
        }
      }

      .demo-description,
      .demo-features {
        margin-top: 20px;

        h3 {
          color: #121314;
          margin-bottom: 10px;
          font-size: 16px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            color: #606266;
            line-height: 1.6;
          }
        }
      }

      .demo-features {
        ul li {
          color: #67c23a;
        }
      }
    }

    .review-result {
      :deep(.el-card__header) {
        background-color: #f0f9ff;
        
        span {
          color: #165dff;
        }
      }

      :deep(.el-alert) {
        border: none;
        background-color: transparent;
        
        .el-alert__title {
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .demo-page {
    padding: 10px;

    .demo-content {
      .el-card {
        .demo-buttons {
          flex-direction: column;
          align-items: center;

          .el-button {
            width: 200px;
          }
        }
      }
    }
  }
}
</style>
