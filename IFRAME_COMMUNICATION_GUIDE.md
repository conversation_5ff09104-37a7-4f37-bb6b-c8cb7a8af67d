# iframe通信功能使用指南

## 🎯 功能概述

实现了完整的父子页面通信功能，支持：
1. **前端假分页** - 人员列表支持分页显示
2. **文件预览优化** - 支持PNG和PDF预览，处理相对路径URL
3. **父子页面通信** - 基于postMessage的双向通信机制

## 📋 新增功能详解

### 1. 前端假分页
- **页面大小**: 8条记录/页
- **分页组件**: 使用Element Plus分页组件
- **搜索支持**: 搜索后重新分页
- **样式优化**: 小尺寸分页器，适合侧边栏

### 2. 文件预览增强
- **支持格式**: PNG、JPG、JPEG、GIF、BMP、WEBP、PDF
- **相对路径处理**: 自动拼接host参数或当前域名
- **URL构建逻辑**:
  ```javascript
  // 如果是完整URL，直接使用
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // 使用host参数拼接
  if (hostUrl.value) {
    return hostUrl.value + '/' + url.replace(/^\//, '');
  }
  
  // 使用当前域名拼接
  return window.location.origin + '/' + url.replace(/^\//, '');
  ```

### 3. 父子页面通信

#### 消息格式
```javascript
{
  type: 'messageType',    // 消息类型
  data: {...},           // 消息数据
  source: 'sourceName'   // 消息来源
}
```

#### 子页面支持的消息类型
- `updateData`: 更新人员数据
- `getReviewComment`: 获取审核意见
- `clearData`: 清空所有数据

#### 子页面发送的消息类型
- `ready`: 页面准备就绪
- `reviewComment`: 返回审核意见数据

## 🚀 使用方法

### 1. 基本iframe嵌入
```html
<iframe 
  src="/personnel-qualification-preview?data=编码数据&host=父页面域名"
  width="100%" 
  height="600px"
  frameborder="0">
</iframe>
```

### 2. 父页面发送消息
```javascript
// 更新数据
iframe.contentWindow.postMessage({
  type: 'updateData',
  data: personnelArray,
  source: 'parent'
}, '*');

// 获取审核意见
iframe.contentWindow.postMessage({
  type: 'getReviewComment',
  data: {},
  source: 'parent'
}, '*');

// 清空数据
iframe.contentWindow.postMessage({
  type: 'clearData',
  data: {},
  source: 'parent'
}, '*');
```

### 3. 父页面接收消息
```javascript
window.addEventListener('message', (event) => {
  if (event.data && event.data.source === 'personnel-qualification-preview') {
    switch (event.data.type) {
      case 'ready':
        console.log('子页面已准备就绪');
        break;
      case 'reviewComment':
        console.log('审核意见:', event.data.data.comment);
        break;
    }
  }
});
```

## 🧪 测试页面

### 访问地址
- **子页面测试**: http://localhost:5173/test
- **父页面通信测试**: http://localhost:5173/parent-test

### 父页面测试功能
1. **发送测试数据** - 向子页面发送人员数据
2. **获取审核意见** - 从子页面获取用户填写的审核意见
3. **清空子页面数据** - 清空子页面所有数据
4. **刷新iframe** - 重新加载子页面
5. **消息日志** - 实时显示所有通信消息

## 📊 数据结构

### URL参数
```
/personnel-qualification-preview?data=编码的JSON数据&host=父页面域名
```

### 人员数据结构
```javascript
[
  {
    "id": 28,
    "renyuanxingming": "测试人员0730-01",
    "gongzhong": "7",
    "zizhi_items": [
      {
        "id": 1,
        "zizhileixing": "1",
        "zizhifujian": {
          "filename": "测试文件.pdf",
          "size": 198027,
          "state": "uploaded",
          "url": "/test/files/test.pdf"  // 相对路径
        }
      }
    ]
  }
]
```

### 审核意见返回格式
```javascript
{
  comment: "审核意见内容",
  selectedPerson: 28,
  selectedQualification: "1"
}
```

## 🎨 界面特性

### 分页组件样式
- 小尺寸按钮 (24px高度)
- 居中对齐
- 简洁的prev/pager/next布局
- 顶部边框分隔

### 文件预览
- **图片**: 居中显示，自适应大小
- **PDF**: iframe内嵌显示
- **其他文件**: 显示文件图标、名称、大小和下载按钮

## 🔧 技术实现

### 分页逻辑
```javascript
const filteredPersonnelList = computed(() => {
  let filtered = personnelList.value;
  
  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(person => 
      person.renyuanxingming.includes(searchKeyword.value) ||
      person.gongzhong.includes(searchKeyword.value)
    );
  }
  
  // 更新总数
  totalPersonnel.value = filtered.length;
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  
  return filtered.slice(start, end);
});
```

### 通信机制
```javascript
// 发送消息
const sendMessageToParent = (type, data) => {
  if (parentWindow.value) {
    parentWindow.value.postMessage({
      type, data, source: 'personnel-qualification-preview'
    }, '*');
  }
};

// 接收消息
const handleParentMessage = (event) => {
  if (event.data && event.data.source === 'parent') {
    // 处理不同类型的消息
  }
};
```

## ✅ 完成状态

- ✅ 前端假分页功能
- ✅ PNG/PDF文件预览
- ✅ 相对路径URL处理
- ✅ 父子页面双向通信
- ✅ 消息日志和调试功能
- ✅ 完整的测试页面
- ✅ 响应式设计适配

现在可以在amis低代码平台中使用iframe组件嵌入，并通过postMessage进行数据交互！
