# 多附件支持功能实现

## 🎯 问题描述

原有的`zizhifujian`（资质附件）字段只支持单个文件对象，现在需要支持多个文件对象的数组格式。

### 原始数据格式
```json
{
  "zizhifujian": {
    "filename": "文件名.扩展名",
    "size": 文件大小,
    "name": "文件名.扩展名", 
    "mimetype": "MIME类型",
    "state": "uploaded",
    "id": "唯一标识符",
    "value": "文件存储路径",
    "url": "文件访问URL"
  }
}
```

### 新的数据格式
```json
{
  "zizhifujian": [
    {
      "filename": "文件1.pdf",
      "size": 1024000,
      "name": "文件1.pdf", 
      "mimetype": "application/pdf",
      "state": "uploaded",
      "id": "file_001",
      "value": "/uploads/file1.pdf",
      "url": "/api/files/file1.pdf"
    },
    {
      "filename": "文件2.jpg",
      "size": 512000,
      "name": "文件2.jpg", 
      "mimetype": "image/jpeg",
      "state": "uploaded",
      "id": "file_002",
      "value": "/uploads/file2.jpg",
      "url": "/api/files/file2.jpg"
    }
  ]
}
```

## 🔧 解决方案

### 1. 类型定义更新

```typescript
// 原始接口保持不变
interface ZizhiFujian {
  id: string;
  filename: string;
  name: string;
  size: number;
  mimetype: string;
  state: string;
  value: string;
  url: string;
}

// 新增支持多种格式的类型定义
type ZizhiFujianData = ZizhiFujian | ZizhiFujian[] | null | undefined;

// 更新Personnel接口
interface Personnel {
  // ... 其他字段
  zizhifujian: ZizhiFujianData; // 支持单个或多个附件
}
```

### 2. 辅助函数

```typescript
// 统一处理附件数据，返回数组格式
const getAttachmentList = (zizhifujian: ZizhiFujianData): ZizhiFujian[] => {
  if (!zizhifujian) return [];
  if (Array.isArray(zizhifujian)) return zizhifujian;
  return [zizhifujian];
};

// 检查是否有已上传的附件
const hasUploadedAttachment = (zizhifujian: ZizhiFujianData): boolean => {
  const attachments = getAttachmentList(zizhifujian);
  return attachments.some(att => att.state === 'uploaded');
};

// 获取第一个已上传的附件
const getFirstUploadedAttachment = (zizhifujian: ZizhiFujianData): ZizhiFujian | null => {
  const attachments = getAttachmentList(zizhifujian);
  return attachments.find(att => att.state === 'uploaded') || null;
};
```

### 3. 响应式数据

```typescript
// 当前选中资质的所有附件
const currentAttachments = computed(() => {
  return getAttachmentList(selectedZizhiItem.value?.zizhifujian);
});

// 当前预览的附件（向后兼容）
const currentAttachment = computed(() => {
  return getFirstUploadedAttachment(selectedZizhiItem.value?.zizhifujian);
});

// 附件切换索引
const currentAttachmentIndex = ref(0);

// 当前显示的附件（支持切换）
const displayAttachment = computed(() => {
  const uploadedAttachments = currentAttachments.value.filter(
    (att: ZizhiFujian) => att.state === "uploaded"
  );
  if (uploadedAttachments.length === 0) return null;
  const index = Math.min(currentAttachmentIndex.value, uploadedAttachments.length - 1);
  return uploadedAttachments[index] || null;
});
```

### 4. 模板更新

#### 附件状态显示
```vue
<div class="upload-status">
  <el-icon v-if="hasUploadedAttachment(qualification.zizhifujian)" color="#00B42A">
    <CircleCheck />
  </el-icon>
  <el-icon v-else color="#909399">
    <CircleClose />
  </el-icon>
  <span :class="['status-text', hasUploadedAttachment(qualification.zizhifujian) ? 'uploaded' : 'not-uploaded']">
    {{
      hasUploadedAttachment(qualification.zizhifujian)
        ? `已上传 (${getAttachmentList(qualification.zizhifujian).filter(att => att.state === 'uploaded').length}个文件)`
        : "未上传"
    }}
  </span>
</div>
```

#### 多附件切换控制
```vue
<div v-if="currentAttachments.filter(att => att.state === 'uploaded').length > 1" class="attachment-controls">
  <div class="attachment-tabs">
    <el-button
      v-for="(attachment, index) in currentAttachments.filter(att => att.state === 'uploaded')"
      :key="attachment.id"
      :type="currentAttachmentIndex === index ? 'primary' : 'default'"
      size="small"
      @click="currentAttachmentIndex = index"
      class="attachment-tab"
    >
      {{ attachment.filename }}
    </el-button>
  </div>
  <div class="attachment-info">
    <span>共 {{ currentAttachments.filter(att => att.state === 'uploaded').length }} 个附件</span>
  </div>
</div>
```

#### 附件预览区域
```vue
<div v-if="displayAttachment" class="preview-area">
  <!-- 图片预览 -->
  <div v-if="isImage(displayAttachment.url)" class="image-preview">
    <img :src="getFullUrl(displayAttachment.url)" :alt="displayAttachment.filename" />
  </div>
  
  <!-- PDF预览 -->
  <div v-else-if="isPdf(displayAttachment.url)" class="pdf-preview">
    <vue-office-pdf :src="getFullUrl(displayAttachment.url)" style="width: 100%; height: 100%" />
  </div>
  
  <!-- 其他文件类型 -->
  <div v-else class="file-preview">
    <el-icon size="48"><Document /></el-icon>
    <p>{{ displayAttachment.filename }}</p>
    <p class="file-size">文件大小: {{ formatFileSize(displayAttachment.size) }}</p>
    <el-button type="primary" @click="downloadFile(displayAttachment)">下载文件</el-button>
  </div>
  
  <!-- 附件详细信息 -->
  <div class="attachment-details">
    <div class="detail-item">
      <span class="label">文件名:</span>
      <span class="value">{{ displayAttachment.filename }}</span>
    </div>
    <div class="detail-item">
      <span class="label">文件大小:</span>
      <span class="value">{{ formatFileSize(displayAttachment.size) }}</span>
    </div>
    <div class="detail-item">
      <span class="label">文件类型:</span>
      <span class="value">{{ displayAttachment.mimetype }}</span>
    </div>
  </div>
</div>
```

### 5. 样式更新

```scss
.attachment-preview {
  .attachment-controls {
    padding: 12px;
    border-bottom: 1px solid #e5e6eb;
    background-color: #fafafa;

    .attachment-tabs {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 8px;

      .attachment-tab {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .attachment-info {
      font-size: 12px;
      color: #909399;
    }
  }

  .attachment-details {
    padding: 12px;
    border-top: 1px solid #e5e6eb;
    background-color: #fafafa;

    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 12px;

      .label {
        color: #909399;
        font-weight: 500;
      }

      .value {
        color: #1d2129;
        word-break: break-all;
      }
    }
  }
}
```

### 6. 事件监听

```typescript
// 监听选中资质变化，重置附件索引
watch(selectedZizhiItem, () => {
  currentAttachmentIndex.value = 0;
  console.log("选中资质变化，重置附件索引");
});
```

## 🎨 功能特性

### ✅ 向后兼容
- 支持原有的单个附件格式
- 支持新的多个附件数组格式
- 无需修改现有数据结构

### ✅ 用户体验
- **多附件显示**: 在状态中显示附件数量 "已上传 (3个文件)"
- **附件切换**: 通过标签页切换不同附件
- **详细信息**: 显示每个附件的详细信息
- **预览支持**: 支持图片、PDF等多种格式预览

### ✅ 开发友好
- **类型安全**: 完整的TypeScript类型定义
- **统一接口**: 通过辅助函数统一处理不同格式
- **易于扩展**: 可以轻松添加新的附件处理逻辑

## 🧪 测试场景

### 1. 单个附件（向后兼容）
```json
{
  "zizhifujian": {
    "filename": "证书.pdf",
    "state": "uploaded",
    // ... 其他字段
  }
}
```

### 2. 多个附件
```json
{
  "zizhifujian": [
    {"filename": "证书正面.jpg", "state": "uploaded"},
    {"filename": "证书背面.jpg", "state": "uploaded"},
    {"filename": "培训记录.pdf", "state": "uploaded"}
  ]
}
```

### 3. 混合状态
```json
{
  "zizhifujian": [
    {"filename": "已上传.pdf", "state": "uploaded"},
    {"filename": "上传中.jpg", "state": "uploading"},
    {"filename": "上传失败.doc", "state": "error"}
  ]
}
```

### 4. 空附件
```json
{
  "zizhifujian": null
}
```

现在系统已经完全支持多附件功能，既保持了向后兼容性，又提供了丰富的多附件管理和预览功能！
