# 培训考试页面预览功能增强

## 🎯 功能概述

为trainingExamination页面添加了完整的预览功能，并修复了PDF预览和URL处理问题：

1. **PDF预览支持** - 添加vue-office-pdf组件支持
2. **预览模态框** - 独立的预览窗口，不影响学习进度
3. **URL处理修复** - 修复getFullUrl方法的host参数处理问题
4. **多格式支持** - 支持Word、Excel、PDF、视频等多种格式预览

## 🔧 主要修改

### 1. PDF预览功能添加

#### 依赖引入
```javascript
import VueOfficePdf from "@vue-office/pdf";
import "@vue-office/pdf/lib/index.css";
```

#### 模板中添加PDF预览
```html
<vue-office-pdf
  style="width: 100%; height: 600px"
  v-else-if="courseType === 'pdf'"
  :src="courseSource"
  @mouseenter="handleCourseFocus"
  @mouseleave="handleCourseBlur"
/>
```

#### 测试数据添加
```javascript
{
  id: 4,
  courseName: "课程4 - PDF文档",
  courseTime: 80,
  studyTime: 0,
  type: "pdf",
  src: "/public/pdf_test.pdf",
}
```

### 2. 预览模态框实现

#### 响应式数据
```javascript
const previewVisible = ref(false);
const previewCourse = ref({
  courseName: "",
  type: "",
  src: "",
});
```

#### 预览方法
```javascript
const showPreview = (course: any) => {
  previewCourse.value = {
    courseName: course.courseName,
    type: course.type,
    src: course.src,
  };
  previewVisible.value = true;
};

const handlePreviewClose = () => {
  previewVisible.value = false;
  previewCourse.value = {
    courseName: "",
    type: "",
    src: "",
  };
};
```

#### 模态框模板
```html
<el-dialog
  v-model="previewVisible"
  title="课程预览"
  width="80%"
  :before-close="handlePreviewClose"
>
  <div class="preview-content">
    <h3>{{ previewCourse.courseName }}</h3>
    <div class="preview-area">
      <!-- 支持多种格式预览 -->
      <vue-office-docx v-if="previewCourse.type === 'docx'" />
      <vue-office-excel v-else-if="previewCourse.type === 'excel'" />
      <vue-office-pdf v-else-if="previewCourse.type === 'pdf'" />
      <video v-else-if="previewCourse.type === 'video'" />
    </div>
  </div>
</el-dialog>
```

### 3. URL处理修复

#### 问题分析
原来的getFullUrl方法在处理host参数时有问题：
```
错误URL: http://localhost:8853/************:8090/object/...
正确URL: http://************:8090/object/...
```

#### 修复方案
```javascript
const getFullUrl = (url: string) => {
  if (!url) return "";

  // 如果已经是完整URL，直接返回
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }

  // 如果有host参数，使用host作为基础URL
  if (hostUrl.value) {
    console.log("Host URL:", hostUrl.value);
    console.log("Original URL:", url);
    
    // 确保host URL不以/结尾，url以/开头
    const baseUrl = hostUrl.value.replace(/\/$/, "");
    const relativePath = url.startsWith("/") ? url : "/" + url;
    const fullUrl = baseUrl + relativePath;
    
    console.log("Generated Full URL:", fullUrl);
    return fullUrl;
  }

  // 否则使用当前域名
  const fullUrl = window.location.origin + "/" + url.replace(/^\//, "");
  console.log("Using current origin:", fullUrl);
  return fullUrl;
};
```

### 4. 操作列添加

#### 表格操作列
```html
<el-table-column label="操作" width="120">
  <template #default="scope">
    <el-button 
      type="primary" 
      size="small" 
      @click="showPreview(scope.row)"
    >
      预览
    </el-button>
  </template>
</el-table-column>
```

## 🎨 样式优化

### 预览模态框样式
```scss
.preview-content {
  h3 {
    margin-bottom: 16px;
    color: #303133;
    font-size: 16px;
  }
  
  .preview-area {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .no-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;
    font-size: 14px;
  }
}
```

### 课程标题样式
```scss
.course-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
```

## 🧪 测试功能

### 测试步骤
1. 访问 http://localhost:5173/training-examination
2. 查看课程列表，包含4种类型的课程
3. 点击"预览"按钮测试预览功能
4. 测试不同格式的文件预览

### 支持的格式
- **Word文档** (.docx) - 使用vue-office-docx
- **Excel表格** (.xlsx) - 使用vue-office-excel  
- **PDF文档** (.pdf) - 使用vue-office-pdf ⭐ 新增
- **视频文件** (.mp4) - 使用HTML5 video标签

## 🔍 调试信息

URL处理的调试输出：
```
Host URL: http://************:8090
Original URL: /object/anZ8mmyELX/s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf
Generated Full URL: http://************:8090/object/anZ8mmyELX/s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf
```

## 📊 功能对比

### 修复前
```
❌ PDF文件无法预览
❌ URL拼接错误: http://localhost:8853/************:8090/...
❌ 只能在主区域查看，影响学习进度
❌ 缺少独立预览功能
```

### 修复后
```
✅ 支持PDF文件预览
✅ URL拼接正确: http://************:8090/...
✅ 独立预览窗口，不影响学习
✅ 支持多种格式预览
✅ 详细的调试信息
```

## ✅ 完成功能

- [x] PDF预览功能添加
- [x] 预览模态框实现
- [x] URL处理问题修复
- [x] 多格式预览支持
- [x] 操作列添加
- [x] 样式优化
- [x] 调试信息完善

## 🚀 使用说明

### 预览功能
1. 在课程列表中点击"预览"按钮
2. 在弹出的模态框中查看课程内容
3. 预览不会影响学习进度统计
4. 支持关闭预览继续正常学习

### URL配置
- 确保host参数正确传递
- 检查文件路径的正确性
- 利用控制台调试信息排查问题

现在trainingExamination页面具备了完整的预览功能，支持多种文件格式，并修复了URL处理问题！
