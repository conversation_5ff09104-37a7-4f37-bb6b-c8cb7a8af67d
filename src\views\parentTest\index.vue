<template>
  <div class="parent-test-page">
    <div class="test-header">
      <h1>父页面通信测试</h1>
      <p>测试与iframe子页面的消息通信功能</p>
    </div>

    <div class="test-content">
      <div class="control-panel">
        <el-card>
          <template #header>
            <span>控制面板</span>
          </template>

          <div class="control-buttons">
            <el-button type="primary" @click="sendDataToChild">
              发送测试数据
            </el-button>

            <el-button type="success" @click="getReviewFromChild">
              获取审核意见
            </el-button>

            <el-button type="warning" @click="clearChildData">
              清空子页面数据
            </el-button>

            <el-button type="info" @click="refreshIframe">
              刷新iframe
            </el-button>

            <el-button
              :type="usePathParams ? 'success' : 'warning'"
              @click="toggleParamType"
            >
              {{ usePathParams ? "路径参数模式" : "查询参数模式" }}
            </el-button>
          </div>

          <div class="message-log">
            <h3>消息日志:</h3>
            <div class="log-container">
              <div
                v-for="(msg, index) in messageLog"
                :key="index"
                :class="['log-item', msg.type]"
              >
                <span class="timestamp">{{ msg.timestamp }}</span>
                <span class="message">{{ msg.message }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <div class="iframe-container">
        <el-card>
          <template #header>
            <span>iframe子页面</span>
          </template>

          <iframe
            ref="iframeRef"
            :src="iframeUrl"
            width="100%"
            height="600px"
            frameborder="0"
            @load="onIframeLoad"
          ></iframe>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";

// 测试数据
const testData = ref([
  {
    shoujihaoma: "17333735973",
    zizhi_items: [],
    guishubumen: "广东拓奇电力技术发展有限公司项目部",
    yuangongzhuangtai: "1",
    qiyemingcheng: "广东拓奇电力技术发展有限公司",
    renyuanxingming: "谢谢谢",
    guishubanzu: "广东拓奇电力技术发展有限公司项目部",
    gongzhong: "jiazigong",
    id: 18,
    zhuanye: "1",
  },
  {
    shoujihaoma: "15079073001",
    zizhi_items: [
      {
        shoujihaoma: "15079073001",
        tezhongzuoyecaozuozhengleixing: "",
        zizhishixiaoriqi: "1755792000",
        zizhifujian: {
          filename: "测试文件.pdf",
          size: 198027,
          name: "测试文件.pdf",
          mimetype: "application/pdf",
          state: "uploaded",
          id: "38c370c8d3e7",
          value: "/test/files/test.pdf",
          url: "/test/files/test.pdf",
        },
        zizhileixing: "1",
        zizhishengxiaoriqi: "1755619200",
        zizhizhuangtai: 1,
        renyuanxingming: "测试人员0730-01",
        id: 1,
      },
    ],
    guishubumen: "2096",
    yuangongzhuangtai: "1",
    qiyemingcheng: "广东拓奇电力技术发展有限公司",
    renyuanxingming: "测试人员0730-01",
    gongzhong: "7",
    id: 28,
    zhuanye: "1",
  },
]);

const iframeRef = ref<HTMLIFrameElement>();
const messageLog = ref<
  Array<{ type: string; message: string; timestamp: string }>
>([]);

// 构建iframe URL - 支持路径参数和查询参数两种方式
const usePathParams = ref(false);
const iframeUrl = computed(() => {
  const dataParam = encodeURIComponent(JSON.stringify(testData.value));
  const hostParam = encodeURIComponent(window.location.origin);

  if (usePathParams.value) {
    // 使用路径参数
    return `/personnel-qualification-preview/${dataParam}?host=${hostParam}`;
  } else {
    // 使用查询参数
    return `/personnel-qualification-preview?data=${dataParam}&host=${hostParam}`;
  }
});

// 添加日志
const addLog = (type: string, message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  messageLog.value.unshift({ type, message, timestamp });

  // 限制日志数量
  if (messageLog.value.length > 50) {
    messageLog.value = messageLog.value.slice(0, 50);
  }
};

// 发送消息给子页面
const sendMessageToChild = (type: string, data: any) => {
  if (iframeRef.value && iframeRef.value.contentWindow) {
    const message = {
      type,
      data,
      source: "parent",
    };

    console.log("发送消息给子页面:", message);
    addLog("sent", `发送: ${type} - ${JSON.stringify(data)}`);

    iframeRef.value.contentWindow.postMessage(message, "*");
  }
};

// 处理子页面消息
const handleChildMessage = (event: MessageEvent) => {
  console.log("接收到子页面消息:", event.data);

  if (event.data && event.data.source === "personnel-qualification-preview") {
    addLog(
      "received",
      `接收: ${event.data.type} - ${JSON.stringify(event.data.data)}`
    );

    switch (event.data.type) {
      case "ready":
        addLog("info", "子页面已准备就绪");
        break;
      case "reviewComment":
        addLog("success", `审核意见: ${event.data.data.comment}`);
        break;
    }
  }
};

// 控制按钮方法
const sendDataToChild = () => {
  sendMessageToChild("updateData", testData.value);
};

const getReviewFromChild = () => {
  sendMessageToChild("getReviewComment", {});
};

const clearChildData = () => {
  sendMessageToChild("clearData", {});
};

const refreshIframe = () => {
  if (iframeRef.value) {
    iframeRef.value.src = iframeUrl.value;
  }
};

const toggleParamType = () => {
  usePathParams.value = !usePathParams.value;
  addLog("info", `切换到${usePathParams.value ? "路径参数" : "查询参数"}模式`);
  // 自动刷新iframe以应用新的参数类型
  setTimeout(() => {
    refreshIframe();
  }, 100);
};

const onIframeLoad = () => {
  addLog("info", "iframe加载完成");
};

onMounted(() => {
  // 监听子页面消息
  window.addEventListener("message", handleChildMessage);
  addLog("info", "父页面已准备就绪，开始监听消息");
});

onUnmounted(() => {
  window.removeEventListener("message", handleChildMessage);
});
</script>

<style scoped lang="scss">
.parent-test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 20px;

    h1 {
      color: #121314;
      margin-bottom: 8px;
    }

    p {
      color: #909399;
      margin: 0;
    }
  }

  .test-content {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 20px;
    max-width: 1400px;
    margin: 0 auto;

    .control-panel {
      .control-buttons {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;

        .el-button {
          justify-content: flex-start;
        }
      }

      .message-log {
        h3 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #121314;
        }

        .log-container {
          max-height: 400px;
          overflow-y: auto;
          border: 1px solid #e5e6eb;
          border-radius: 4px;
          padding: 8px;
          background-color: #fafafa;

          .log-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 1.4;

            &.sent {
              color: #165dff;
            }

            &.received {
              color: #00b42a;
            }

            &.info {
              color: #909399;
            }

            &.success {
              color: #00b42a;
              font-weight: 600;
            }

            .timestamp {
              min-width: 80px;
              margin-right: 8px;
              color: #c9cdd4;
            }

            .message {
              flex: 1;
              word-break: break-all;
            }
          }
        }
      }
    }

    .iframe-container {
      iframe {
        border-radius: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .parent-test-page {
    .test-content {
      grid-template-columns: 1fr;

      .control-panel {
        order: 2;
      }

      .iframe-container {
        order: 1;
      }
    }
  }
}
</style>
