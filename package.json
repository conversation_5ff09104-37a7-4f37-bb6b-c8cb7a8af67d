{"name": "demo1", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --open", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint src", "fix": "eslint src --fix", "format": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\"", "lint:eslint": "eslint src/**/*.{ts,vue} --cache --fix", "lint:style": "stylelint src/**/*.{css,scss,vue} --cache --fix", "prepare": "husky install", "commit": "git add . && cz-customizable", "changelog": "conventional-changelog -p cz-config.js -i CHANGELOG.md -s -r 0"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@types/crypto-js": "^4.1.2", "@types/lodash": "^4.14.197", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "autofit.js": "^3.0.4", "axios": "^1.4.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "element-plus": "^2.3.9", "http-server": "^14.1.1", "js-md5": "^0.7.3", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "qs": "^6.11.2", "qweather-icons": "^1.6.0", "ts-loader": "^9.4.4", "vue": "^3.3.4", "vue-demi": "^0.14.6", "vue-router": "^4.2.4", "vue3-seamless-scroll": "^2.0.1", "vue3-slide-verify": "^1.1.4"}, "devDependencies": {"@babel/eslint-parser": "^7.22.10", "@commitlint/config-conventional": "^17.7.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.1", "@vitejs/plugin-vue": "^4.2.3", "commitizen": "^4.3.0", "commitlint": "^17.7.1", "commitlint-config-cz": "^0.13.3", "commitlint-config-git-commit-emoji": "^1.0.0", "conventional-changelog": "^4.0.0", "conventional-changelog-cli": "^3.0.0", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.0", "mockjs": "^1.1.0", "postcss": "^8.4.28", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.7", "prettier": "^3.0.2", "sass": "^1.66.1", "sass-loader": "^13.3.2", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^10.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.3", "stylelint-scss": "^5.1.0", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-mock": "^2.9.6", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}