<template>
  <div class="personnel-qualification-entry">
    <div class="page-header">
      <h1>人员资质录入页面</h1>
      <p>这是一个演示页面，展示如何使用批量预览功能</p>
    </div>

    <div class="content-area">
      <!-- 模拟的人员资质录入表格 -->
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <span>人员资质列表</span>
            <el-button type="primary" @click="openBatchPreview">
              批量预览
            </el-button>
          </div>
        </template>

        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="workType" label="工种" width="120" />
          <el-table-column prop="qualificationCount" label="资质数量" width="100" />
          <el-table-column prop="uploadedCount" label="已上传" width="100" />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" @click="editRow(scope.row)">编辑</el-button>
              <el-button size="small" type="primary" @click="previewRow(scope.row)">
                预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 审核意见区域 -->
      <el-card class="review-card">
        <template #header>
          <span>审核意见</span>
        </template>
        
        <el-form :model="reviewForm" label-width="100px">
          <el-form-item label="审核意见">
            <el-input
              v-model="reviewForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见..."
              readonly
            />
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select v-model="reviewForm.status" placeholder="请选择审核状态">
              <el-option label="待审核" value="pending" />
              <el-option label="审核通过" value="approved" />
              <el-option label="审核不通过" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitReview">提交审核</el-button>
            <el-button @click="resetReview">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 批量预览组件 -->
    <PersonnelQualificationPreview 
      ref="previewRef" 
      @review-comment="handleReviewComment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import PersonnelQualificationPreview from '@/views/personnelQualificationPreview/index.vue'

// 表格数据
const tableData = ref([
  {
    id: '1',
    name: '丁春洋',
    workType: '普通工人',
    qualificationCount: 7,
    uploadedCount: 3,
    status: '部分完成'
  },
  {
    id: '2',
    name: '刘飞',
    workType: '安装人员',
    qualificationCount: 4,
    uploadedCount: 3,
    status: '部分完成'
  },
  {
    id: '3',
    name: '张三',
    workType: '普通工人',
    qualificationCount: 5,
    uploadedCount: 5,
    status: '全部完成'
  },
  {
    id: '4',
    name: '李四',
    workType: '安装人员',
    qualificationCount: 6,
    uploadedCount: 2,
    status: '部分完成'
  }
])

// 审核表单
const reviewForm = reactive({
  comment: '',
  status: 'pending'
})

// 预览组件引用
const previewRef = ref()

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case '全部完成':
      return 'success'
    case '部分完成':
      return 'warning'
    case '未开始':
      return 'info'
    default:
      return 'info'
  }
}

const openBatchPreview = () => {
  // 打开批量预览弹窗，传递表格数据
  previewRef.value?.openDialog({
    personnelList: tableData.value,
    mode: 'batch'
  })
}

const editRow = (row: any) => {
  ElMessage.info(`编辑 ${row.name} 的资质信息`)
}

const previewRow = (row: any) => {
  // 打开单个人员的预览
  previewRef.value?.openDialog({
    personnelList: [row],
    mode: 'single',
    selectedPersonId: row.id
  })
}

const handleReviewComment = (comment: string) => {
  // 接收从预览组件传递过来的审核意见
  reviewForm.comment = comment
  ElMessage.success('审核意见已填充')
}

const submitReview = () => {
  if (!reviewForm.comment.trim()) {
    ElMessage.warning('请填写审核意见')
    return
  }
  
  // 提交审核逻辑
  ElMessage.success('审核提交成功')
  console.log('提交审核:', reviewForm)
}

const resetReview = () => {
  reviewForm.comment = ''
  reviewForm.status = 'pending'
}

// 监听URL参数，支持iframe传递数据
const handleUrlParams = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const tableDataParam = urlParams.get('tableData')
  
  if (tableDataParam) {
    try {
      const decodedData = JSON.parse(decodeURIComponent(tableDataParam))
      tableData.value = decodedData
      console.log('从URL参数接收到表格数据:', decodedData)
    } catch (error) {
      console.error('解析URL参数失败:', error)
    }
  }
}

// 组件挂载时处理URL参数
handleUrlParams()
</script>

<style scoped lang="scss">
.personnel-qualification-entry {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 20px;
    text-align: center;

    h1 {
      color: #121314;
      margin-bottom: 8px;
    }

    p {
      color: #909399;
      margin: 0;
    }
  }

  .content-area {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .table-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-weight: 600;
          color: #121314;
        }
      }

      :deep(.el-table) {
        .el-table__header {
          th {
            background-color: #fafafa;
            color: #121314;
            font-weight: 600;
          }
        }
      }
    }

    .review-card {
      :deep(.el-card__header) {
        background-color: #fafafa;
        
        span {
          font-weight: 600;
          color: #121314;
        }
      }

      .el-form {
        .el-form-item {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .el-textarea {
          :deep(.el-textarea__inner) {
            border-radius: 4px;
            background-color: #f9f9f9;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .personnel-qualification-entry {
    padding: 10px;

    .content-area {
      .table-card {
        :deep(.el-table) {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
