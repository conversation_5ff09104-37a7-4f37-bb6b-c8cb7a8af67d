# 人员资质预览页面 - 最终使用说明

## 🎯 页面特性

### ✅ 已优化功能
1. **完全移除弹窗**: 适配低代码平台弹窗嵌入
2. **自定义滚动条**: 美观的滚动条样式
3. **路由参数支持**: 通过URL传递数据
4. **调试信息完整**: 控制台输出详细调试信息
5. **响应式布局**: 三栏布局适配不同尺寸

## 🚀 在低代码平台中使用

### 1. iframe嵌入方式
```html
<iframe 
  src="http://localhost:5173/personnel-qualification-preview?data=编码后的JSON数据"
  width="100%" 
  height="500px"
  frameborder="0">
</iframe>
```

### 2. 数据传递格式
```javascript
const personnelData = [
  {
    "shoujihaoma": "17333735973",
    "guishubumen": "广东拓奇电力技术发展有限公司项目部", 
    "yuangongzhuangtai": "1",
    "qiyemingcheng": "广东拓奇电力技术发展有限公司",
    "renyuanxingming": "谢谢谢",
    "guishubanzu": "广东拓奇电力技术发展有限公司项目部",
    "gongzhong": "jiazigong",
    "id": 18,
    "zhuanye": "1"
  }
  // ... 更多人员数据
];

// 编码数据
const encodedData = encodeURIComponent(JSON.stringify(personnelData));

// 构建URL
const iframeUrl = `http://localhost:5173/personnel-qualification-preview?data=${encodedData}`;
```

### 3. 获取审核意见
```javascript
// 在父页面中获取iframe中的审核意见
const iframe = document.getElementById('preview-iframe');
const previewWindow = iframe.contentWindow;

// 调用iframe中的方法获取审核意见
const reviewComment = previewWindow.getReviewComment();
console.log('审核意见:', reviewComment);
```

## 🔧 调试功能

### 控制台输出信息
页面会在浏览器控制台输出以下调试信息：

```
=== 调试信息 ===
路由参数 route.query: {data: "..."}
data参数: "编码后的JSON字符串"
解析后的数据: [{id: 18, renyuanxingming: "谢谢谢", ...}, ...]
设置人员列表: [{...}, {...}, ...]
选择人员: 18
选择资质类型: "1-1"
```

### 测试步骤
1. 启动项目: `npm run dev`
2. 访问测试页面: http://localhost:5173/test
3. 点击"测试路由参数传递"
4. 查看控制台调试信息
5. 验证数据显示是否正确

## 📋 页面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    预览内容区域 (500px高)                      │
├──────────────┬──────────────┬─────────────────────────────────┤
│   人员选择    │   资质类型    │        附件预览区域              │
│              │              │                                │
│ • 谢谢谢      │ * 特种作业证  │   ┌─────────────────────────┐    │
│   [架子工]    │   ✓ 已上传   │   │                        │    │
│              │              │   │     图片/PDF预览        │    │
│ • 研究工程师  │ * 健康证明   │   │                        │    │
│   [安装人员]  │   ✗ 未上传   │   │                        │    │
│              │              │   └─────────────────────────┘    │
│ [搜索框]     │              │                                │
│              │              │   审核意见:                     │
│              │              │   ┌─────────────────────────┐    │
│              │              │   │                        │    │
│              │              │   │  [文本框]               │    │
│              │              │   │                        │    │
│              │              │   └─────────────────────────┘    │
└──────────────┴──────────────┴─────────────────────────────────┘
```

## 🎨 样式特性

### 自定义滚动条
- 宽度: 6px
- 轨道颜色: #f2f3f5
- 滑块颜色: #c9cdd4
- 悬停颜色: #a8abb2
- 圆角: 3px

### 工种标签映射
- `jiazigong` → 架子工 (info)
- `7` → 普通工人 (info)  
- `8` → 安装人员 (warning)
- 其他 → 工种{代码} (info)

## 🔄 数据流程

```
1. 低代码平台 → 传递人员数据 → iframe URL参数
2. 预览页面 → 接收参数 → 解析JSON数据
3. 用户操作 → 选择人员 → 加载资质类型
4. 用户操作 → 选择资质 → 加载附件预览
5. 用户输入 → 填写审核意见 → 暴露给外部调用
```

## ⚠️ 注意事项

1. **数据格式**: 确保传递的JSON数据格式正确
2. **URL编码**: 必须对JSON数据进行URL编码
3. **跨域问题**: 注意iframe的跨域访问限制
4. **高度设置**: 建议iframe高度设置为500px以上
5. **调试模式**: 开发时可查看控制台调试信息

## 🎉 完成状态

- ✅ 移除el-dialog弹窗
- ✅ 优化滚动条样式  
- ✅ 修复动态导入错误
- ✅ 完善调试信息
- ✅ 适配低代码平台
- ✅ 支持iframe嵌入
- ✅ 数据传递功能
- ✅ 审核意见获取

现在可以在低代码平台的弹窗中正常使用了！
