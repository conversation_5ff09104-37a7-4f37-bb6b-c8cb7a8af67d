# 路径参数支持实现

## 🎯 功能概述

预览页面现在支持两种数据传递方式：
1. **查询参数** (原有方式): `/personnel-qualification-preview?data=编码数据`
2. **路径参数** (新增方式): `/personnel-qualification-preview/编码数据` ⭐

## 🔧 实现细节

### 1. 路由配置更新
```typescript
// src/router/routes.ts
export const constantRoute: Array<RouteRecordRaw> = [
  // 原有路由 - 支持查询参数
  {
    path: "/personnel-qualification-preview",
    name: "PersonnelQualificationPreview",
    component: () => import("@/views/personnelQualificationPreview/index.vue"),
  },
  // 新增路由 - 支持路径参数 ⭐
  {
    path: "/personnel-qualification-preview/:data",
    name: "PersonnelQualificationPreviewWithData",
    component: () => import("@/views/personnelQualificationPreview/index.vue"),
  },
];
```

### 2. 参数处理逻辑优化
```javascript
const initializeData = () => {
  // 优先从路径参数获取data
  const pathDataParam = route.params.data as string;
  const queryDataParam = route.query.data as string;
  const hostParam = route.query.host as string;
  
  // 优先使用路径参数，如果没有则使用查询参数
  const dataParam = pathDataParam || queryDataParam;
  
  if (dataParam) {
    try {
      const parsedData = JSON.parse(decodeURIComponent(dataParam));
      // 处理数据...
    } catch (error) {
      console.error("解析数据失败:", error);
    }
  }
};
```

### 3. 调试信息增强
```javascript
console.log("=== 调试信息 ===");
console.log("路由参数 route.params:", route.params);
console.log("路由参数 route.query:", route.query);
console.log("路径data参数:", pathDataParam);
console.log("查询data参数:", queryDataParam);
console.log("host参数:", hostParam);
```

## 🧪 测试功能

### 1. 测试页面新增功能
访问 http://localhost:5173/test，新增了：
- **测试路径参数传递** 按钮 (红色) ⭐

### 2. 父页面测试增强
访问 http://localhost:5173/parent-test，新增了：
- **参数模式切换** 按钮
- 支持在路径参数和查询参数之间切换
- 自动刷新iframe应用新的参数类型

## 📊 两种方式对比

### 查询参数方式 (原有)
```
URL: /personnel-qualification-preview?data=编码数据&host=域名
优点: 
- 传统方式，兼容性好
- 参数清晰可见
- 支持多个查询参数

缺点:
- URL较长
- 特殊字符需要编码
```

### 路径参数方式 (新增)
```
URL: /personnel-qualification-preview/编码数据?host=域名
优点:
- URL相对简洁
- RESTful风格
- 数据作为路径的一部分

缺点:
- 数据较大时URL仍然很长
- 需要额外的路由配置
```

## 🚀 使用方法

### 1. 查询参数方式 (兼容原有)
```javascript
const dataString = encodeURIComponent(JSON.stringify(data));
const url = `/personnel-qualification-preview?data=${dataString}&host=${host}`;
```

### 2. 路径参数方式 (新增)
```javascript
const dataString = encodeURIComponent(JSON.stringify(data));
const url = `/personnel-qualification-preview/${dataString}?host=${host}`;
```

### 3. Vue Router编程式导航
```javascript
// 查询参数方式
router.push({
  path: '/personnel-qualification-preview',
  query: {
    data: encodeURIComponent(JSON.stringify(data)),
    host: window.location.origin
  }
});

// 路径参数方式
router.push({
  path: `/personnel-qualification-preview/${encodeURIComponent(JSON.stringify(data))}`,
  query: {
    host: window.location.origin
  }
});
```

## 🔍 测试场景

### 场景1: 查询参数测试
1. 访问测试页面
2. 点击"测试路由参数传递"
3. 查看URL格式: `?data=...&host=...`

### 场景2: 路径参数测试
1. 访问测试页面
2. 点击"测试路径参数传递" ⭐
3. 查看URL格式: `/编码数据?host=...`

### 场景3: 父页面模式切换
1. 访问父页面测试
2. 点击"查询参数模式/路径参数模式"按钮
3. 观察iframe URL的变化
4. 验证两种模式都能正常工作

## 📋 兼容性说明

### 向后兼容
- ✅ 原有的查询参数方式完全保留
- ✅ 现有的集成代码无需修改
- ✅ 两种方式可以并存使用

### 优先级规则
```javascript
// 参数获取优先级
const dataParam = route.params.data || route.query.data;
```
1. **路径参数优先**: 如果URL中有路径参数，优先使用
2. **查询参数备用**: 如果没有路径参数，使用查询参数
3. **调试信息完整**: 两种参数都会在控制台显示

## ✅ 验证清单

- [x] 路径参数路由配置正确
- [x] 参数解析逻辑支持两种方式
- [x] 测试页面新增路径参数测试
- [x] 父页面支持模式切换
- [x] 调试信息完整显示
- [x] 向后兼容性保持
- [x] 优先级规则正确

## 🎉 使用建议

### 推荐使用场景
- **查询参数**: 适合传统Web应用，参数较多的场景
- **路径参数**: 适合RESTful API风格，单一数据传递场景

### 最佳实践
1. 根据具体需求选择合适的参数传递方式
2. 在同一个项目中保持一致的风格
3. 利用调试信息验证参数传递是否正确
4. 测试两种方式的兼容性

现在预览页面支持灵活的数据传递方式，可以根据不同的使用场景选择最合适的方案！
