# 速搭对话框iframe广播问题解决方案

## 🎯 问题分析

根据您提供的速搭平台配置，发现了广播监听问题的根本原因：

### 配置结构分析
```
对话框 (Dialog)
├── iframe (您的预览页面)
└── 确定按钮 (发送广播: ff28d12e814a_dialogConfirm)
```

### 问题所在
1. **作用域隔离**：iframe内的页面与对话框按钮不在同一个作用域
2. **广播范围**：速搭的广播可能只在对话框层级传播，不会传递到iframe内部
3. **消息传递机制**：需要通过postMessage在iframe和父窗口间传递

## 🔧 解决方案

### 1. 预览页面增强监听

已添加的功能：

#### 多层级消息监听
```javascript
// 基础广播监听
window.addEventListener("message", handleSudaBroadcast);

// 全局调试监听
window.addEventListener("message", (event) => {
  console.log("=== 全局消息监听 ===");
  console.log("事件源:", event.origin);
  console.log("事件数据:", event.data);
  
  // 特别检查广播字段
  if (event.data && typeof event.data === 'object') {
    const hasEventName = 'eventName' in event.data;
    const hasActionType = 'actionType' in event.data;
    console.log("广播字段检查:", { hasEventName, hasActionType });
  }
});
```

#### 父窗口通信
```javascript
// 检测父窗口并建立通信
if (window.parent && window.parent !== window) {
  console.log("检测到父窗口，添加父窗口消息监听");
  
  // 向父窗口发送准备就绪消息
  window.parent.postMessage({
    type: 'iframe_ready',
    source: 'personnel_preview',
    timestamp: Date.now()
  }, '*');
}
```

#### 宽松的广播检测
```javascript
const isSpeedaBroadcast =
  (messageData && messageData.type === "broadcast") ||
  (messageData && messageData.actionType === "broadcast") ||
  (messageData && messageData.eventName) ||
  (messageData && messageData.eventName === "ff28d12e814a_dialogConfirm") ||
  (messageData && typeof messageData === "object" && Object.keys(messageData).length > 0);
```

### 2. 速搭平台配置优化

#### 方案A：修改确定按钮配置

将确定按钮的广播改为向iframe发送消息：

```json
{
  "type": "button",
  "actionType": "confirm",
  "label": "确定",
  "primary": true,
  "onEvent": {
    "click": {
      "actions": [
        {
          "actionType": "custom",
          "script": "const iframe = document.querySelector('iframe[src*=\"personnel-qualification-preview\"]'); if (iframe && iframe.contentWindow) { iframe.contentWindow.postMessage({ type: 'broadcast', eventName: 'ff28d12e814a_dialogConfirm', data: { action: 'confirm', timestamp: Date.now() } }, '*'); } else { console.log('未找到iframe'); }"
        }
      ]
    }
  }
}
```

#### 方案B：添加中间层处理

在对话框中添加一个隐藏的组件来监听广播并转发给iframe：

```json
{
  "type": "tpl",
  "tpl": "",
  "onEvent": {
    "broadcast_ff28d12e814a_dialogConfirm": {
      "actions": [
        {
          "actionType": "custom",
          "script": "const iframe = document.querySelector('iframe[src*=\"personnel-qualification-preview\"]'); if (iframe && iframe.contentWindow) { iframe.contentWindow.postMessage({ type: 'broadcast', eventName: 'ff28d12e814a_dialogConfirm', data: event.data }, '*'); }"
        }
      ]
    }
  }
}
```

#### 方案C：使用自定义JS（推荐）

在确定按钮的点击事件中添加自定义JS：

```javascript
// 自定义JS代码
console.log('确定按钮被点击');

// 1. 发送原有的广播
doAction({
  actionType: 'broadcast',
  eventName: 'ff28d12e814a_dialogConfirm'
});

// 2. 同时向iframe发送消息
const iframe = document.querySelector('iframe[src*="personnel-qualification-preview"]');
if (iframe && iframe.contentWindow) {
  console.log('找到iframe，发送消息');
  iframe.contentWindow.postMessage({
    type: 'broadcast',
    eventName: 'ff28d12e814a_dialogConfirm',
    actionType: 'broadcast',
    data: {
      action: 'confirm',
      timestamp: Date.now(),
      source: 'dialog_button'
    }
  }, '*');
} else {
  console.log('未找到iframe或iframe未加载完成');
  
  // 延迟重试
  setTimeout(() => {
    const retryIframe = document.querySelector('iframe[src*="personnel-qualification-preview"]');
    if (retryIframe && retryIframe.contentWindow) {
      retryIframe.contentWindow.postMessage({
        type: 'broadcast',
        eventName: 'ff28d12e814a_dialogConfirm',
        actionType: 'broadcast',
        data: {
          action: 'confirm',
          timestamp: Date.now(),
          source: 'dialog_button_retry'
        }
      }, '*');
    }
  }, 100);
}
```

### 3. 完整的速搭配置示例

```json
{
  "type": "button",
  "actionType": "confirm",
  "label": "确定",
  "primary": true,
  "id": "u:7e520ade4819",
  "onEvent": {
    "click": {
      "actions": [
        {
          "actionType": "broadcast",
          "eventName": "ff28d12e814a_dialogConfirm"
        },
        {
          "actionType": "custom",
          "script": "const iframe = document.querySelector('iframe[src*=\"personnel-qualification-preview\"]'); if (iframe && iframe.contentWindow) { console.log('向iframe发送确认消息'); iframe.contentWindow.postMessage({ type: 'broadcast', eventName: 'ff28d12e814a_dialogConfirm', actionType: 'broadcast', data: { action: 'confirm', timestamp: Date.now() } }, '*'); } else { console.log('iframe未找到，延迟重试'); setTimeout(() => { const retryIframe = document.querySelector('iframe[src*=\"personnel-qualification-preview\"]'); if (retryIframe && retryIframe.contentWindow) { retryIframe.contentWindow.postMessage({ type: 'broadcast', eventName: 'ff28d12e814a_dialogConfirm', actionType: 'broadcast', data: { action: 'confirm', timestamp: Date.now() } }, '*'); } }, 100); }"
        }
      ]
    }
  },
  "close": false
}
```

## 🧪 测试步骤

### 1. 验证iframe通信
1. 打开对话框
2. 查看控制台是否显示：`检测到父窗口，添加父窗口消息监听`
3. 查看是否有iframe准备就绪的消息

### 2. 验证广播接收
1. 点击确定按钮
2. 查看控制台是否显示：
   ```
   === 全局消息监听 ===
   事件数据: {type: "broadcast", eventName: "ff28d12e814a_dialogConfirm", ...}
   === 接收到速搭广播消息 ===
   事件名称: ff28d12e814a_dialogConfirm
   ```

### 3. 验证业务逻辑
1. 确认是否触发了审核意见返回
2. 检查是否调用了相应的处理函数

## 💡 调试技巧

### 1. 在速搭平台中添加调试
```javascript
// 在确定按钮的自定义JS中添加
console.log('=== 速搭平台调试 ===');
console.log('当前页面URL:', window.location.href);
console.log('iframe列表:', document.querySelectorAll('iframe'));

const iframe = document.querySelector('iframe[src*="personnel-qualification-preview"]');
console.log('目标iframe:', iframe);
console.log('iframe src:', iframe ? iframe.src : 'not found');
console.log('iframe contentWindow:', iframe ? iframe.contentWindow : 'not found');
```

### 2. 检查iframe加载状态
```javascript
// 在预览页面中添加
console.log('=== iframe状态检查 ===');
console.log('当前URL:', window.location.href);
console.log('父窗口:', window.parent);
console.log('是否在iframe中:', window.parent !== window);
console.log('document.referrer:', document.referrer);
```

## ✅ 预期结果

成功配置后，点击确定按钮应该看到：

```
=== 速搭平台调试 ===
找到iframe，发送消息
=== 全局消息监听 ===
事件数据: {type: "broadcast", eventName: "ff28d12e814a_dialogConfirm", ...}
=== 接收到速搭广播消息 ===
事件名称: ff28d12e814a_dialogConfirm
检测到确认事件，返回审核意见
```

现在预览页面已经具备了完整的调试和监听能力，关键是要在速搭平台的确定按钮中添加向iframe发送消息的逻辑！
