# 速搭iframe广播集成指南

## 🎯 功能概述

在预览页面中集成了速搭平台的iframe广播监听功能，支持接收和处理来自速搭平台的广播消息。

## 🔧 预览页面集成

### 1. 广播监听函数

```javascript
// 速搭广播监听处理
const handleSudaBroadcast = (event: MessageEvent) => {
  // 检查是否是速搭平台的广播消息
  if (event.data && event.data.type === 'broadcast') {
    console.log("=== 接收到速搭广播消息 ===");
    console.log("事件名称:", event.data.eventName);
    console.log("广播数据:", event.data.data);
    
    // 根据不同的广播事件执行相应操作
    switch (event.data.eventName) {
      case 'broadcast_1':
        handleBroadcast1(event.data.data);
        break;
      case 'personnel_update':
        handlePersonnelUpdate(event.data.data);
        break;
      case 'refresh_data':
        handleRefreshData(event.data.data);
        break;
      default:
        console.log("未处理的广播事件:", event.data.eventName);
    }
  }
};
```

### 2. 具体事件处理函数

#### broadcast_1事件处理
```javascript
const handleBroadcast1 = (data: any) => {
  console.log("处理broadcast_1事件:", data);
  
  // 示例：根据角色信息更新界面
  if (data.myrole) {
    console.log("用户角色:", data.myrole);
    // 可以根据角色显示不同的功能或权限
  }
  
  if (data.age) {
    console.log("用户年龄:", data.age);
  }
  
  // 可以在这里添加具体的业务逻辑
  // 例如：更新界面状态、刷新数据等
};
```

#### 人员更新事件处理
```javascript
const handlePersonnelUpdate = (data: any) => {
  console.log("处理人员更新事件:", data);
  
  if (data.personnelList && Array.isArray(data.personnelList)) {
    // 更新人员列表
    initializeData(data.personnelList);
    console.log("通过广播更新人员列表");
  }
};
```

#### 数据刷新事件处理
```javascript
const handleRefreshData = (data: any) => {
  console.log("处理数据刷新事件:", data);
  
  // 重新初始化数据
  initializeData();
  console.log("通过广播刷新数据");
};
```

### 3. 事件监听注册

```javascript
onMounted(() => {
  // 组件挂载后初始化数据
  initializeData();

  // 监听父页面消息
  window.addEventListener("message", handleParentMessage);
  
  // 监听速搭广播消息 ⭐ 新增
  window.addEventListener("message", handleSudaBroadcast);

  // 向父页面发送准备就绪消息
  setTimeout(() => {
    sendMessageToParent("ready", {
      message: "子页面已准备就绪",
      timestamp: Date.now(),
    });
  }, 100);
});

onUnmounted(() => {
  cleanup();
});

const cleanup = () => {
  window.removeEventListener("message", handleParentMessage);
  window.removeEventListener("message", handleSudaBroadcast); // ⭐ 新增
};
```

## 🚀 速搭平台配置指南

### 1. 在速搭平台中添加广播监听

#### 方法一：通过组件事件配置

1. **选择目标组件**
   - 在速搭平台中选择需要监听广播的组件
   - 进入组件的事件配置面板

2. **添加广播监听事件**
   - 事件类型：选择 "广播监听"
   - 事件名称：输入要监听的广播事件名（如：`broadcast_1`）
   - 处理方式：选择 "自定义JS"

3. **配置监听处理代码**
```javascript
// 广播监听处理函数
function onBroadcastReceived(eventName, data) {
  console.log('接收到广播:', eventName, data);
  
  // 根据事件名称执行不同操作
  switch(eventName) {
    case 'broadcast_1':
      // 处理broadcast_1事件
      handleBroadcast1(data);
      break;
    case 'personnel_update':
      // 处理人员更新事件
      handlePersonnelUpdate(data);
      break;
    default:
      console.log('未处理的广播事件:', eventName);
  }
}

// 注册广播监听
window.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'broadcast') {
    onBroadcastReceived(event.data.eventName, event.data.data);
  }
});
```

#### 方法二：通过页面级别配置

1. **进入页面设置**
   - 在速搭平台中打开页面设置
   - 找到 "页面事件" 或 "全局事件" 配置

2. **添加页面级广播监听**
   - 事件类型：页面加载完成
   - 执行代码：添加全局广播监听

```javascript
// 页面级广播监听配置
(function() {
  // 广播事件处理映射
  const broadcastHandlers = {
    'broadcast_1': function(data) {
      console.log('处理broadcast_1:', data);
      // 可以调用页面中的其他函数或更新组件状态
    },
    'personnel_update': function(data) {
      console.log('处理人员更新:', data);
      // 更新相关组件的数据
    },
    'refresh_data': function(data) {
      console.log('处理数据刷新:', data);
      // 刷新页面数据
    }
  };

  // 注册全局广播监听
  window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'broadcast') {
      const handler = broadcastHandlers[event.data.eventName];
      if (handler) {
        handler(event.data.data);
      } else {
        console.log('未找到处理器:', event.data.eventName);
      }
    }
  });
})();
```

### 2. 发送广播消息

#### 在速搭平台组件中发送广播

```javascript
// 自定义JS代码示例
const myMsg = '我是自定义JS';
doAction({
  actionType: 'broadcast',
  args: {
    eventName: "broadcast_1"
  },
  data: {
    myrole: "${role}",
    age: 18,
    timestamp: Date.now(),
    source: 'suda_platform'
  }
});
```

#### 发送人员更新广播

```javascript
// 发送人员列表更新广播
doAction({
  actionType: 'broadcast',
  args: {
    eventName: "personnel_update"
  },
  data: {
    personnelList: [
      // 人员数据数组
      {
        id: 1,
        renyuanxingming: "张三",
        gongzhong: "架子工",
        // ... 其他字段
      }
    ],
    updateTime: Date.now()
  }
});
```

#### 发送数据刷新广播

```javascript
// 发送数据刷新广播
doAction({
  actionType: 'broadcast',
  args: {
    eventName: "refresh_data"
  },
  data: {
    refreshType: 'full',
    timestamp: Date.now()
  }
});
```

## 📊 广播消息格式

### 标准广播消息结构
```javascript
{
  type: 'broadcast',           // 消息类型
  eventName: 'broadcast_1',    // 事件名称
  data: {                      // 广播数据
    myrole: "admin",
    age: 18,
    // ... 其他自定义数据
  }
}
```

### 支持的事件类型

| 事件名称 | 描述 | 数据格式 |
|---------|------|---------|
| `broadcast_1` | 基础广播事件 | `{myrole: string, age: number}` |
| `personnel_update` | 人员数据更新 | `{personnelList: Personnel[]}` |
| `refresh_data` | 数据刷新 | `{refreshType: string}` |

## 🧪 测试验证

### 1. 控制台调试
打开浏览器控制台，查看广播消息的接收和处理日志：

```
=== 接收到速搭广播消息 ===
事件名称: broadcast_1
广播数据: {myrole: "admin", age: 18}
处理broadcast_1事件: {myrole: "admin", age: 18}
用户角色: admin
用户年龄: 18
```

### 2. 功能测试
1. **基础广播测试**：发送broadcast_1事件，验证预览页面是否正确接收
2. **数据更新测试**：发送personnel_update事件，验证人员列表是否更新
3. **刷新测试**：发送refresh_data事件，验证数据是否重新加载

## ✅ 集成清单

- [x] 添加速搭广播监听函数
- [x] 实现多种事件处理器
- [x] 注册事件监听器
- [x] 添加事件清理机制
- [x] 提供详细的调试日志
- [x] 支持多种广播事件类型

现在预览页面已经完全集成了速搭平台的广播功能，可以接收和处理来自速搭平台的各种广播消息！
