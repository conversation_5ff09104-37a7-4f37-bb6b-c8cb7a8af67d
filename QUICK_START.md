# 人员资质批量预览功能 - 快速启动指南

## 🚀 快速开始

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 访问演示页面
打开浏览器访问: http://localhost:5173/demo

### 3. 测试功能
- 点击"打开批量预览"按钮测试批量预览功能
- 点击"前往录入页面"查看完整的录入流程

## 📋 功能清单

### ✅ 已实现功能

1. **批量预览弹窗** - 基于Figma设计稿的高保真还原
   - 三栏布局：人员选择 | 资质类型 | 附件预览
   - 支持人员搜索功能
   - 支持资质类型切换
   - 支持多种附件格式预览

2. **审核意见功能** - 固定文本框，支持意见传递
   - 关闭预览时自动填充到父组件
   - 支持事件传递机制

3. **iframe数据传递** - 支持URL参数传递表格数据
   - 自动解析URL参数中的tableData
   - 支持JSON格式数据传递

4. **响应式设计** - 适配不同屏幕尺寸
   - 桌面端完整布局
   - 移动端优化显示

5. **通用化设计** - 可复用的组件架构
   - 支持不同数据源
   - 可配置的数据结构

## 🎯 核心页面

| 页面 | 路径 | 说明 |
|------|------|------|
| 演示页面 | `/demo` | 功能演示和测试 |
| 录入页面 | `/personnel-qualification-entry` | 人员资质录入主页面 |
| 预览组件 | `/personnel-qualification-preview` | 独立的预览组件 |

## 🔧 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **样式处理**: SCSS
- **路由管理**: Vue Router
- **构建工具**: Vite

## 📱 设计还原

基于Figma设计稿实现的功能包括：

1. **图1 - 批量预览按钮**: 在人员资质录入页面添加批量预览功能入口
2. **图2 - 预览弹窗**: 三栏布局的预览界面，支持人员和资质类型切换
3. **图3 - 附件预览**: 右侧附件预览区域，支持图片、PDF等格式

## 🎨 界面特性

- **左侧人员列表**: 姓名+工种标签，支持搜索和选择
- **中间资质类型**: 显示必填项标识(*)和上传状态图标
- **右侧附件预览**: 支持图片直接预览，PDF内嵌显示
- **底部审核意见**: 固定文本框，支持意见填写和传递

## 🔄 数据流

```
父组件 → 打开预览 → 传递人员数据
预览组件 → 选择人员 → 加载资质类型
预览组件 → 选择资质 → 加载附件预览
预览组件 → 填写意见 → 关闭时传递给父组件
```

## 🌐 iframe集成

支持通过iframe方式集成，可以通过URL参数传递数据：

```javascript
const iframeUrl = `/personnel-qualification-entry?tableData=${encodeURIComponent(JSON.stringify(data))}`
```

## 📝 使用示例

```vue
<template>
  <PersonnelQualificationPreview 
    ref="previewRef"
    @review-comment="handleReviewComment"
  />
</template>

<script setup>
const previewRef = ref()

// 打开预览
const openPreview = () => {
  previewRef.value?.openDialog(data)
}

// 处理审核意见
const handleReviewComment = (comment) => {
  // 处理审核意见
}
</script>
```

## 🎉 完成状态

所有需求功能已完成实现：
- ✅ 批量预览功能
- ✅ 三栏布局界面
- ✅ 人员和资质切换
- ✅ 附件预览功能
- ✅ 审核意见传递
- ✅ iframe数据传递
- ✅ 响应式设计
- ✅ 通用化架构

现在可以启动项目并访问 `/demo` 页面来体验完整功能！
