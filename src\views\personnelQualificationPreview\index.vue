<template>
  <div class="personnel-qualification-preview">
    <div class="preview-content">
      <!-- 左侧人员选择 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>人员选择</h3>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索"
            class="search-input"
            clearable
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="personnel-list">
          <div
            v-for="person in filteredPersonnelList"
            :key="person.id"
            :class="[
              'personnel-item',
              { active: selectedPersonId === person.id },
            ]"
            @click="selectPerson(person.id)"
          >
            <div class="person-info">
              <span class="person-name">{{ person.renyuanxingming }}</span>
              <el-tag :type="getPersonTagType(person.gongzhong)" size="small">
                {{ getWorkTypeName(person.gongzhong) }}
              </el-tag>
            </div>
          </div>

          <!-- 分页组件 - 固定在底部，始终显示 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              :page-size="pageSize"
              :total="totalPersonnel"
              layout="prev, pager, next"
              :small="true"
              @current-change="handlePageChange"
              :hide-on-single-page="false"
            />
          </div>
        </div>
      </div>

      <!-- 中间资质类型选择 -->
      <div class="middle-panel">
        <div class="panel-header">
          <h3>类型选择</h3>
        </div>

        <div class="qualification-list">
          <div
            v-for="qualification in currentPersonQualifications"
            :key="qualification.id"
            :class="[
              'qualification-item',
              {
                active: selectedQualificationId == qualification.id,
              },
            ]"
            @click="selectQualification(qualification.id)"
          >
            <div class="qualification-info">
              <span class="qualification-name">
                {{ getQualificationTypeName(qualification.zizhileixing) }}
              </span>
              <div class="upload-status">
                <el-icon
                  v-if="
                    qualification.zizhifujian &&
                    qualification.zizhifujian.state === 'uploaded'
                  "
                  color="#00B42A"
                >
                  <CircleCheck />
                </el-icon>
                <el-icon v-else color="#909399">
                  <CircleClose />
                </el-icon>
                <span
                  :class="[
                    'status-text',
                    qualification.zizhifujian &&
                    qualification.zizhifujian.state === 'uploaded'
                      ? 'uploaded'
                      : 'not-uploaded',
                  ]"
                >
                  {{
                    qualification.zizhifujian &&
                    qualification.zizhifujian.state === "uploaded"
                      ? "已上传"
                      : "未上传"
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧附件预览 -->
      <div class="right-panel">
        <div class="attachment-preview">
          <div v-if="currentAttachment" class="preview-area">
            <!-- 图片预览 -->
            <div v-if="isImage(currentAttachment.url)" class="image-preview">
              <div class="image-container">
                <img
                  :src="getFullUrl(currentAttachment.url)"
                  :alt="currentAttachment.filename"
                  @click="
                    openImagePreview(
                      getFullUrl(currentAttachment.url),
                      currentAttachment.filename
                    )
                  "
                />
                <div class="image-overlay">
                  <el-icon
                    class="zoom-icon"
                    @click="
                      openImagePreview(
                        getFullUrl(currentAttachment.url),
                        currentAttachment.filename
                      )
                    "
                  >
                    <ZoomIn />
                  </el-icon>
                </div>
              </div>
            </div>

            <!-- PDF预览 -->
            <div v-else-if="isPdf(currentAttachment.url)" class="pdf-preview">
              <vue-office-pdf
                :src="getFullUrl(currentAttachment.url)"
                style="width: 100%; height: 100%"
                @rendered="onPdfRendered"
                @error="onPdfError"
              />
            </div>

            <!-- 其他文件类型 -->
            <div v-else class="file-preview">
              <el-icon size="48"><Document /></el-icon>
              <p>{{ currentAttachment.filename }}</p>
              <p class="file-size">
                文件大小: {{ formatFileSize(currentAttachment.size) }}
              </p>
              <el-button
                type="primary"
                @click="downloadFile(currentAttachment)"
              >
                下载文件
              </el-button>
            </div>
          </div>

          <div v-else class="no-attachment">
            <el-empty description="暂无附件" />
          </div>
        </div>

        <!-- 审核意见 -->
        <div class="review-section">
          <div class="review-header">
            <label>*审核意见</label>
          </div>
          <el-input
            v-model="reviewComment"
            type="textarea"
            :rows="4"
            placeholder="内容"
            class="review-textarea"
          />
        </div>
      </div>
    </div>

    <!-- 图片放大预览模态框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      :title="imagePreviewTitle"
      width="80%"
      :before-close="closeImagePreview"
      class="image-preview-dialog"
    >
      <div class="image-preview-content">
        <img
          :src="imagePreviewUrl"
          :alt="imagePreviewTitle"
          class="preview-image"
        />
      </div>
      <template #footer>
        <el-button @click="closeImagePreview">关闭</el-button>
        <el-button type="primary" @click="downloadCurrentImage"
          >下载图片</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, defineExpose } from "vue";
import { useRoute } from "vue-router";
import {
  Search,
  CircleCheck,
  CircleClose,
  Document,
  ZoomIn,
  Close,
} from "@element-plus/icons-vue";
import VueOfficePdf from "@vue-office/pdf";
import "@vue-office/pdf/lib/index.css";

const route = useRoute();

// 接口定义
interface Personnel {
  id: number;
  renyuanxingming: string; // 人员姓名
  gongzhong: string; // 工种
  shoujihaoma: string; // 手机号码
  guishubumen: string; // 归属部门
  yuangongzhuangtai: string; // 员工状态
  qiyemingcheng: string; // 企业名称
  guishubanzu: string; // 归属班组
  zhuanye: string; // 专业
  zizhi_items: ZizhiItem[]; // 资质项目数组
}

interface ZizhiItem {
  id: number;
  renyuanxingming: string;
  zizhileixing: string; // 资质类型
  zizhizhuangtai: number; // 资质状态
  zizhishengxiaoriqi: string; // 生效日期
  zizhishixiaoriqi: string; // 失效日期
  tezhongzuoyecaozuozhengleixing: string; // 特种作业操作证类型
  zizhifujian: ZizhiFujian; // 资质附件
}

interface ZizhiFujian {
  id: string;
  filename: string;
  name: string;
  size: number;
  mimetype: string;
  state: string;
  value: string;
  url: string;
}

// 响应式数据
const searchKeyword = ref("");
const selectedPersonId = ref<number | null>(null);
const selectedQualificationId = ref("");
const reviewComment = ref("");

// 人员数据
const personnelList = ref<Personnel[]>([]);
const selectedZizhiItem = ref<ZizhiItem | null>(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(8);
const totalPersonnel = ref(0);

// 父页面通信
const hostUrl = ref("");

// 图片预览相关
const imagePreviewVisible = ref(false);
const imagePreviewUrl = ref("");
const imagePreviewTitle = ref("");

// 搜索过滤后的完整列表
const searchFilteredList = computed(() => {
  let filtered = personnelList.value;

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(
      (person: Personnel) =>
        person.renyuanxingming.includes(searchKeyword.value) ||
        person.gongzhong.includes(searchKeyword.value)
    );
  }

  return filtered;
});

// 分页后的显示列表
const filteredPersonnelList = computed(() => {
  const filtered = searchFilteredList.value;

  // 更新总数
  totalPersonnel.value = filtered.length;

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;

  return filtered.slice(start, end);
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(totalPersonnel.value / pageSize.value);
});

const currentPersonQualifications = computed(() => {
  if (selectedPersonId.value === null) return [];
  const person = personnelList.value.find(
    (p: Personnel) => p.id === selectedPersonId.value
  );
  return person && person.zizhi_items && Array.isArray(person.zizhi_items)
    ? person.zizhi_items
    : [];
});

const currentAttachment = computed(() => {
  return selectedZizhiItem.value?.zizhifujian || null;
});

// 数据初始化处理
const initializeData = (data?: Personnel[]) => {
  console.log("=== 调试信息 ===");

  // 如果直接传入数据，优先使用
  if (data && Array.isArray(data)) {
    personnelList.value = data;
    console.log("直接接收数据:", data);
  } else {
    // 否则从路由参数获取
    const pathDataParam = route.params.data as string;
    const queryDataParam = route.query.data as string;
    const hostParam = route.query.host as string;

    console.log("路由参数 route.params:", route.params);
    console.log("路由参数 route.query:", route.query);
    console.log("路径data参数:", pathDataParam);
    console.log("查询data参数:", queryDataParam);
    console.log("host参数:", hostParam);

    // 设置父页面URL
    if (hostParam) {
      hostUrl.value = hostParam;
      // 安全地检查是否在iframe中，避免跨域访问问题
      try {
        if (window.parent && window.parent !== window) {
          // 不直接赋值parentWindow，避免跨域访问问题
          console.log("检测到iframe环境");
        }
      } catch (error) {
        console.warn("检查iframe环境时出错:", error);
      }
    }

    // 优先使用路径参数，如果没有则使用查询参数
    const dataParam = pathDataParam || queryDataParam;

    if (dataParam) {
      try {
        const parsedData = JSON.parse(decodeURIComponent(dataParam));
        console.log("解析后的数据:", parsedData);

        if (Array.isArray(parsedData)) {
          personnelList.value = parsedData;
        }
      } catch (error) {
        console.error("解析数据失败:", error);
      }
    } else {
      console.log("未找到data参数");
    }
  }

  console.log("最终人员列表:", personnelList.value);

  // 重置分页
  currentPage.value = 1;

  // 默认选择第一个人员
  if (Array.isArray(personnelList.value) && personnelList.value.length > 0) {
    const firstPerson = personnelList.value[0];
    if (firstPerson && typeof firstPerson.id !== "undefined") {
      selectPerson(firstPerson.id);
    }
  }
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  console.log("切换到第", page, "页");
};

// 监听搜索关键词变化，重置页码
watch(searchKeyword, () => {
  currentPage.value = 1;
  console.log("搜索关键词变化，重置到第1页");
});

const selectPerson = (personId: number) => {
  selectedPersonId.value = personId;
  selectedQualificationId.value = "";
  selectedZizhiItem.value = null;
  console.log("选择人员:", personId);

  // 默认选择第一个资质项目
  const person = personnelList.value.find((p: Personnel) => p.id === personId);
  if (
    person &&
    person.zizhi_items &&
    Array.isArray(person.zizhi_items) &&
    person.zizhi_items.length > 0
  ) {
    selectQualification(person.zizhi_items[0].id);
  }
};

const selectQualification = (qualificationId: number) => {
  selectedQualificationId.value = qualificationId.toString();
  console.log("选择资质类型:", qualificationId);

  // 找到对应的资质项目
  const person = personnelList.value.find(
    (p: Personnel) => p.id === selectedPersonId.value
  );
  if (person && person.zizhi_items && Array.isArray(person.zizhi_items)) {
    selectedZizhiItem.value =
      person.zizhi_items.find(
        (item: ZizhiItem) => item.id === qualificationId
      ) || null;
  }
};

const getPersonTagType = (gongzhong: string) => {
  // 根据工种返回标签类型
  switch (gongzhong) {
    case "8": // 安装人员
      return "warning";
    case "jiazigong": // 架子工
    case "7": // 普通工人
      return "info";
    default:
      return "info";
  }
};

// 工种枚举映射
const workTypeOptions = [
  { label: "普工", value: "1" },
  { label: "电工", value: "2" },
  { label: "架子工", value: "3" },
  { label: "焊工", value: "4" },
  { label: "登高作业工", value: "5" },
  { label: "输煤检修工", value: "6" },
  { label: "锅炉检修工", value: "7" },
  { label: "汽机检修工", value: "8" },
  { label: "运行值班员", value: "9" },
  { label: "运行巡检员", value: "10" },
  { label: "电动机检修工", value: "11" },
  { label: "起重司机", value: "12" },
  { label: "起重指挥工", value: "13" },
  { label: "起重司索工", value: "14" },
  { label: "驾驶员", value: "15" },
  { label: "锅炉司炉工", value: "16" },
  { label: "化验员", value: "17" },
  { label: "汽车驾驶员", value: "18" },
  { label: "锅炉运行值班员", value: "19" },
  { label: "燃料值班员", value: "20" },
  { label: "汽轮机运行值班员", value: "21" },
  { label: "燃气轮机值班员", value: "22" },
  { label: "发电集值班员控", value: "23" },
  { label: "电气值班员", value: "24" },
  { label: "火电厂氢冷值班员", value: "25" },
  { label: "余热余压利用系统操作工", value: "26" },
  { label: "水力发电运行值班员", value: "27" },
  { label: "光伏发电运维值班员", value: "28" },
  { label: "锅炉操作工", value: "29" },
  { label: "风力发电运维值班员", value: "30" },
  { label: "供热管网系统运行工", value: "31" },
  { label: "变配电运行值班员", value: "32" },
  { label: "继电保护员", value: "33" },
  { label: "燃气储运工", value: "34" },
  { label: "气体深冷分离工", value: "35" },
  { label: "工业气体生产工", value: "36" },
  { label: "工业气体生产工", value: "37" },
  { label: "工业气体液化工", value: "38" },
  { label: "工业废气治理工", value: "39" },
  { label: "压缩机操作工", value: "40" },
  { label: "风机操作工", value: "41" },
  { label: "水生产处理工", value: "42" },
  { label: "水供应输排工", value: "43" },
  { label: "工业废水处理工", value: "44" },
  { label: "司泵工", value: "45" },
  { label: "管廊运维员", value: "46" },
  { label: "砌筑工", value: "47" },
  { label: "石工", value: "48" },
  { label: "混凝土工", value: "49" },
  { label: "钢筋工", value: "50" },
  { label: "装配式建筑施工员", value: "51" },
  { label: "乡村建设工匠", value: "52" },
  { label: "铁路自轮运转设备工", value: "53" },
  { label: "铁路线桥工", value: "54" },
  { label: "筑路工", value: "55" },
  { label: "公路养护工", value: "56" },
  { label: "桥隧工", value: "57" },
  { label: "凿岩工", value: "58" },
  { label: "爆破工", value: "59" },
  { label: "防水工", value: "60" },
  { label: "水运工程施工工", value: "61" },
  { label: "水工建构筑物维护检修工", value: "62" },
  { label: "电力电缆安装运维工", value: "63" },
  { label: "送配电线路工", value: "64" },
  { label: "牵引电力线路安装维护工", value: "65" },
  { label: "舟桥工", value: "66" },
  { label: "管道工", value: "67" },
  { label: "铁路综合维修工", value: "68" },
  { label: "城市轨道交通检修工", value: "69" },
  { label: "机械设备安装工", value: "70" },
  { label: "电气设备安装工", value: "71" },
  { label: "电梯安装维修工", value: "72" },
  { label: "管工", value: "73" },
  { label: "制冷空调系统安装维修工", value: "74" },
  { label: "锅炉设备安装工", value: "75" },
  { label: "发电设备安装工", value: "76" },
  { label: "电力电气设备安装工", value: "77" },
  { label: "装饰装修工", value: "78" },
  { label: "建筑门窗幕墙安装工", value: "79" },
  { label: "照明工程施工员", value: "80" },
  { label: "专用车辆驾驶员", value: "81" },
  { label: "起重装卸机械操作工", value: "82" },
  { label: "起重工", value: "83" },
  { label: "输送机操作工", value: "84" },
  { label: "索道运输机械操作工", value: "85" },
  { label: "挖掘铲运和桩工机械司机", value: "86" },
  { label: "设备点检员", value: "87" },
  { label: "机修钳工", value: "88" },
  { label: "仪器仪表维修工", value: "89" },
  { label: "锅炉设备检修工", value: "90" },
  { label: "汽轮机和水轮机检修工", value: "91" },
  { label: "电机检修工", value: "92" },
  { label: "变电设备检修工", value: "93" },
  { label: "工程机械维修工", value: "94" },
  { label: "机电设备维修工", value: "95" },
  { label: "船舶修理工", value: "96" },
  { label: "化学检验员", value: "97" },
  { label: "物理性能检验员", value: "98" },
  { label: "生化检验员", value: "99" },
  { label: "无损检测员", value: "100" },
  { label: "质检员", value: "101" },
  { label: "试验员", value: "102" },
  { label: "称重计量工", value: "103" },
  { label: "包装工", value: "104" },
  { label: "安全员", value: "105" },
  { label: "工业机器人系统运维员", value: "106" },
  { label: "工业视觉系统运维员", value: "107" },
  { label: "其他", value: "108" },
  { label: "单位负责人", value: "109" },
  { label: "物业外委", value: "110" },
  { label: "机务", value: "111" },
];

const getWorkTypeName = (gongzhong: string) => {
  // 兼容旧的字符串格式
  if (gongzhong === "jiazigong") return "架子工";

  // 根据工种代码返回工种名称
  const workType = workTypeOptions.find((item) => item.value === gongzhong);
  return workType ? workType.label : `工种${gongzhong}`;
};

// 人员资质类型枚举映射
const qualificationTypeOptions = [
  { label: "身份证_国徽面", value: "1" },
  { label: "身份证_人像面", value: "2" },
  { label: "劳动合同", value: "3" },
  { label: "保险", value: "4" },
  { label: "体检报告", value: "5" },
  { label: "特种作业操作证", value: "6" },
];

const getQualificationTypeName = (zizhileixing: string) => {
  // 根据资质类型代码返回资质类型名称
  const qualificationType = qualificationTypeOptions.find(
    (item) => item.value === zizhileixing
  );
  return qualificationType
    ? qualificationType.label
    : `资质类型${zizhileixing}`;
};

const formatFileSize = (bytes: number) => {
  // 格式化文件大小
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// URL处理 - 支持相对路径
const getFullUrl = (url: string) => {
  if (!url) return "";

  // 如果已经是完整URL，直接返回
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }

  // 如果有host参数，使用host作为基础URL
  if (hostUrl.value) {
    console.log("Host URL:", hostUrl.value);
    console.log("Original URL:", url);

    // 确保host URL不以/结尾，url以/开头
    const baseUrl = hostUrl.value.replace(/\/$/, "");
    const relativePath = url.startsWith("/") ? url : "/" + url;
    const fullUrl = "http://************:8090" + relativePath;

    console.log("Generated Full URL:", fullUrl);
    return fullUrl;
  }

  // 否则使用当前域名
  const fullUrl = window.location.origin + "/" + url.replace(/^\//, "");
  console.log("Using current origin:", fullUrl);
  return fullUrl;
};

const isImage = (url: string) => {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
};

const isPdf = (url: string) => {
  return /\.pdf$/i.test(url);
};

const downloadFile = (attachment: ZizhiFujian) => {
  // 实现文件下载逻辑
  const link = document.createElement("a");
  link.href = getFullUrl(attachment.url);
  link.download = attachment.filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 图片预览相关方法
const openImagePreview = (url: string, title: string) => {
  imagePreviewUrl.value = url;
  imagePreviewTitle.value = title;
  imagePreviewVisible.value = true;
};

const closeImagePreview = () => {
  imagePreviewVisible.value = false;
  imagePreviewUrl.value = "";
  imagePreviewTitle.value = "";
};

const downloadCurrentImage = () => {
  if (imagePreviewUrl.value) {
    const link = document.createElement("a");
    link.href = imagePreviewUrl.value;
    link.download = imagePreviewTitle.value || "image";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// PDF预览相关方法
const onPdfRendered = () => {
  console.log("PDF渲染成功");
};

const onPdfError = (error: any) => {
  console.error("PDF渲染失败:", error);
};

// 父子页面通信功能
const sendMessageToParent = (type: string, data: any) => {
  try {
    // 检查是否在iframe中
    if (window.parent && window.parent !== window) {
      const message = {
        type,
        data,
        source: "personnel-qualification-preview",
      };
      console.log("发送消息给父页面:", message);
      window.parent.postMessage(message, "*");
    } else {
      console.log("不在iframe环境中，无法发送消息给父页面");
    }
  } catch (error) {
    console.warn("发送消息给父页面失败:", error);
  }
};

const handleParentMessage = (event: MessageEvent) => {
  console.log("接收到消息:", event.data);

  if (event.data && event.data.type) {
    switch (event.data.type) {
      case "updateData":
        // 更新数据
        if (event.data.data && Array.isArray(event.data.data)) {
          personnelList.value = event.data.data;
          currentPage.value = 1;
          console.log("更新人员数据:", event.data.data);

          // 默认选择第一个人员
          if (personnelList.value.length > 0) {
            selectPerson(personnelList.value[0].id);
          }
        }
        break;
      case "setPersonnelData":
        // 直接设置人员数据
        if (event.data.data && Array.isArray(event.data.data)) {
          console.log("直接设置人员数据:", event.data.data);
          initializeData(event.data.data);
        }
        break;
      case "getReviewComment":
        // 返回审核意见
        sendMessageToParent("reviewComment", {
          comment: reviewComment.value,
          selectedPerson: selectedPersonId.value,
          selectedQualification: selectedQualificationId.value,
        });
        break;
      case "clearData":
        // 清空数据
        personnelList.value = [];
        selectedPersonId.value = null;
        selectedQualificationId.value = "";
        selectedZizhiItem.value = null;
        reviewComment.value = "";
        currentPage.value = 1;
        break;
    }
  }
};

// 暴露审核意见给外部调用
const getReviewComment = () => {
  return reviewComment.value;
};

// 直接设置数据的方法
const setPersonnelData = (data: Personnel[]) => {
  console.log("直接设置人员数据:", data);
  initializeData(data);
};

// 暴露方法给外部调用
defineExpose({
  getReviewComment,
  sendMessageToParent,
  setPersonnelData,
  initializeData,
});

onMounted(() => {
  // 组件挂载后初始化数据
  initializeData();

  // 监听父页面消息
  window.addEventListener("message", handleParentMessage);

  // 向父页面发送准备就绪消息
  setTimeout(() => {
    sendMessageToParent("ready", {
      message: "子页面已准备就绪",
      timestamp: Date.now(),
    });
  }, 100);
});

// 组件卸载时清理事件监听
const cleanup = () => {
  window.removeEventListener("message", handleParentMessage);
};

// 监听页面卸载
window.addEventListener("beforeunload", cleanup);
</script>

<style scoped lang="scss">
.personnel-qualification-preview {
  width: 100%;
  height: 100%;
  background-color: #fff;

  .preview-content {
    display: flex;
    height: 600px;
    border: 1px solid #e5e6eb;

    .left-panel {
      width: 334px;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;
      position: relative;
      height: 100%; // 确保占满高度

      .panel-header {
        padding: 16px 16px 12px 16px;
        border-bottom: 1px solid #e5e6eb;
        flex-shrink: 0;

        h3 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }

        .search-input {
          :deep(.el-input__wrapper) {
            border-radius: 2px;
          }
        }
      }

      .personnel-list {
        flex: 1;
        overflow-y: auto;
        padding: 0;
        min-height: 0; // 允许flex子项收缩
        height: 100%; // 占满可用高度

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f2f3f5;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c9cdd4;
          border-radius: 3px;

          &:hover {
            background: #a8abb2;
          }
        }

        .personnel-item {
          padding: 12px 16px;
          min-height: 48px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 0;
          margin: 0;
          border-bottom: 1px solid #f2f3f5;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 3px solid #165dff;
            margin-left: 0;
            padding-left: 13px; // 减少左边距以补偿边框

            .person-name {
              color: #165dff;
            }
          }

          .person-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .person-name {
              font-size: 14px;
              color: #121314;
            }

            .el-tag {
              border-radius: 34px;
              font-size: 12px;
            }
          }
        }
      }

      .pagination-wrapper {
        padding: 6px 16px;
        border-top: 1px solid #e5e6eb;
        display: flex;
        justify-content: center;
        background-color: #fff;
        flex-shrink: 0; // 防止被压缩
        min-height: 36px; // 固定最小高度

        :deep(.el-pagination) {
          .el-pager li {
            min-width: 20px;
            height: 20px;
            line-height: 20px;
            font-size: 11px;
            margin: 0 2px;
            border-radius: 2px;
          }

          .btn-prev,
          .btn-next {
            min-width: 20px;
            height: 20px;
            line-height: 20px;
            font-size: 11px;
            margin: 0 2px;

            .el-icon {
              font-size: 10px;
            }
          }

          // 当只有一页时也显示分页组件
          &.is-background .btn-prev,
          &.is-background .btn-next {
            background-color: #f5f7fa;
          }

          // 禁用状态样式
          .btn-prev:disabled,
          .btn-next:disabled {
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
    }

    .middle-panel {
      flex: 1;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 16px;
        border-bottom: 1px solid #e5e6eb;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }
      }

      .qualification-list {
        flex: 1;
        overflow-y: auto;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f2f3f5;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c9cdd4;
          border-radius: 3px;

          &:hover {
            background: #a8abb2;
          }
        }

        .qualification-item {
          padding: 0 16px;
          height: 40px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 2px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 2px solid #165dff;

            .qualification-name {
              color: #165dff;
            }
          }

          &.required {
            padding-left: 27px;
          }

          .qualification-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .qualification-name {
              font-size: 14px;
              color: #121314;
            }

            .upload-status {
              display: flex;
              align-items: center;
              gap: 4px;

              .status-text {
                font-size: 12px;

                &.uploaded {
                  color: #00b42a;
                }

                &.not-uploaded {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 24px;
      gap: 16px;

      .attachment-preview {
        flex: 1;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        overflow: hidden;

        .preview-area {
          width: 100%;
          height: 100%;

          .image-preview {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;

            .image-container {
              position: relative;
              display: inline-block;
              cursor: pointer;

              img {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
                transition: transform 0.3s ease;
              }

              .image-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.3s ease;

                .zoom-icon {
                  color: white;
                  font-size: 24px;
                  cursor: pointer;
                }
              }

              &:hover {
                .image-overlay {
                  opacity: 1;
                }

                img {
                  transform: scale(1.05);
                }
              }
            }
          }

          .pdf-preview {
            width: 100%;
            height: 100%;

            iframe {
              width: 100%;
              height: 100%;
            }
          }

          .file-preview {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 16px;
            color: #909399;

            p {
              margin: 0;
              font-size: 14px;
            }

            .file-size {
              font-size: 12px;
              color: #c9cdd4;
            }
          }
        }

        .no-attachment {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .review-section {
        .review-header {
          margin-bottom: 8px;

          label {
            font-size: 14px;
            color: #5c5c5c;
          }
        }

        .review-textarea {
          :deep(.el-textarea__inner) {
            min-height: 100px;
            border-radius: 2px;
          }
        }
      }
    }
  }
}

// 图片预览对话框样式
:deep(.image-preview-dialog) {
  .el-dialog__body {
    padding: 0;
  }

  .image-preview-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background-color: #000;

    .preview-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
    }
  }
}
</style>
