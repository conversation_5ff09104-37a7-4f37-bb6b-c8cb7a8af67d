<template>
  <div class="personnel-qualification-preview">
    <!-- 页面内容 -->
    <div class="page-header">
      <h1>资质预览</h1>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div class="preview-content">
        <!-- 左侧人员选择 -->
        <div class="left-panel">
          <div class="panel-header">
            <h3>人员选择</h3>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索"
              class="search-input"
              clearable
            >
              <template #suffix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="personnel-list">
            <div
              v-for="person in filteredPersonnelList"
              :key="person.id"
              :class="[
                'personnel-item',
                { active: selectedPersonId === person.id },
              ]"
              @click="selectPerson(person.id)"
            >
              <div class="person-info">
                <span class="person-name">{{ person.renyuanxingming }}</span>
                <el-tag :type="getPersonTagType(person.gongzhong)" size="small">
                  {{ getWorkTypeName(person.gongzhong) }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 滚动条 -->
          <div class="scroll-indicator">
            <div class="scroll-track">
              <div class="scroll-thumb" :style="scrollThumbStyle"></div>
            </div>
          </div>
        </div>

        <!-- 中间资质类型选择 -->
        <div class="middle-panel">
          <div class="panel-header">
            <h3>类型选择</h3>
          </div>

          <div class="qualification-list">
            <div
              v-for="qualification in currentPersonQualifications"
              :key="qualification.id"
              :class="[
                'qualification-item',
                {
                  active: selectedQualificationId === qualification.id,
                  required: qualification.required,
                },
              ]"
              @click="selectQualification(qualification.id)"
            >
              <div class="qualification-info">
                <span class="qualification-name">
                  {{ qualification.required ? "* " : ""
                  }}{{ qualification.name }}
                </span>
                <div class="upload-status">
                  <el-icon v-if="qualification.uploaded" color="#00B42A">
                    <CircleCheck />
                  </el-icon>
                  <el-icon v-else color="#909399">
                    <CircleClose />
                  </el-icon>
                  <span
                    :class="[
                      'status-text',
                      qualification.uploaded ? 'uploaded' : 'not-uploaded',
                    ]"
                  >
                    {{ qualification.uploaded ? "已上传" : "未上传" }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧附件预览 -->
        <div class="right-panel">
          <div class="attachment-preview">
            <div v-if="currentAttachment" class="preview-area">
              <!-- 图片预览 -->
              <div v-if="isImage(currentAttachment.url)" class="image-preview">
                <img
                  :src="currentAttachment.url"
                  :alt="currentAttachment.name"
                />
              </div>

              <!-- PDF预览 -->
              <div v-else-if="isPdf(currentAttachment.url)" class="pdf-preview">
                <iframe :src="currentAttachment.url" frameborder="0"></iframe>
              </div>

              <!-- 其他文件类型 -->
              <div v-else class="file-preview">
                <el-icon size="48"><Document /></el-icon>
                <p>{{ currentAttachment.name }}</p>
                <el-button
                  type="primary"
                  @click="downloadFile(currentAttachment)"
                >
                  下载文件
                </el-button>
              </div>
            </div>

            <div v-else class="no-attachment">
              <el-empty description="暂无附件" />
            </div>
          </div>

          <!-- 审核意见 -->
          <div class="review-section">
            <div class="review-header">
              <label>*审核意见</label>
            </div>
            <el-input
              v-model="reviewComment"
              type="textarea"
              :rows="4"
              placeholder="内容"
              class="review-textarea"
            />
          </div>
        </div>
      </div>

      <div class="page-footer">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  Search,
  CircleCheck,
  CircleClose,
  Document,
} from "@element-plus/icons-vue";

const route = useRoute();
const router = useRouter();

// 接口定义
interface Personnel {
  id: number;
  renyuanxingming: string; // 人员姓名
  gongzhong: string; // 工种
  shoujihaoma: string; // 手机号码
  guishubumen: string; // 归属部门
  yuangongzhuangtai: string; // 员工状态
  qiyemingcheng: string; // 企业名称
  guishubanzu: string; // 归属班组
  zhuanye: string; // 专业
}

interface Qualification {
  id: string;
  name: string;
  uploaded: boolean;
  required: boolean;
}

interface Attachment {
  id: string;
  name: string;
  url: string;
  type: string;
}

// 响应式数据
const searchKeyword = ref("");
const selectedPersonId = ref<number | null>(null);
const selectedQualificationId = ref("");
const reviewComment = ref("");

// 人员数据
const personnelList = ref<Personnel[]>([]);

const qualificationsMap = ref<Record<string, Qualification[]>>({
  "1": [
    { id: "1-1", name: "特种作业证", uploaded: true, required: true },
    { id: "1-2", name: "健康证明", uploaded: false, required: true },
    { id: "1-3", name: "三级教育证明", uploaded: true, required: true },
    { id: "1-4", name: "考试成绩证明", uploaded: true, required: true },
    { id: "1-5", name: "其他", uploaded: false, required: false },
    { id: "1-6", name: "劳动合同", uploaded: false, required: false },
    { id: "1-7", name: "身份证", uploaded: false, required: true },
  ],
  "2": [
    { id: "2-1", name: "特种作业证", uploaded: true, required: true },
    { id: "2-2", name: "健康证明", uploaded: true, required: true },
    { id: "2-3", name: "三级教育证明", uploaded: false, required: true },
    { id: "2-4", name: "考试成绩证明", uploaded: true, required: true },
  ],
});

const attachmentsMap = ref<Record<string, Attachment>>({
  "1-1": {
    id: "1-1",
    name: "特种作业证.jpg",
    url: "/api/files/cert1.jpg",
    type: "image",
  },
  "1-3": {
    id: "1-3",
    name: "三级教育证明.pdf",
    url: "/api/files/education.pdf",
    type: "pdf",
  },
  "2-1": {
    id: "2-1",
    name: "特种作业证.jpg",
    url: "/api/files/cert2.jpg",
    type: "image",
  },
});

// 计算属性
const filteredPersonnelList = computed(() => {
  if (!searchKeyword.value) return personnelList.value;
  return personnelList.value.filter(
    (person) =>
      person.renyuanxingming.includes(searchKeyword.value) ||
      person.gongzhong.includes(searchKeyword.value)
  );
});

const currentPersonQualifications = computed(() => {
  if (selectedPersonId.value === null) return [];
  return qualificationsMap.value[selectedPersonId.value.toString()] || [];
});

const currentAttachment = computed(() => {
  return attachmentsMap.value[selectedQualificationId.value] || null;
});

const scrollThumbStyle = computed(() => {
  const totalItems = personnelList.value.length;
  const visibleItems = Math.min(8, totalItems);
  const thumbHeight = (visibleItems / totalItems) * 100;
  return {
    height: `${thumbHeight}%`,
    top: "9.3%",
  };
});

// 路由参数处理
const initializeData = () => {
  const dataParam = route.query.data as string;

  console.log("=== 调试信息 ===");
  console.log("路由参数 route.query:", route.query);
  console.log("data参数:", dataParam);

  if (dataParam) {
    try {
      const parsedData = JSON.parse(decodeURIComponent(dataParam));
      console.log("解析后的数据:", parsedData);

      if (Array.isArray(parsedData)) {
        personnelList.value = parsedData;
        console.log("设置人员列表:", personnelList.value);

        // 默认选择第一个人员
        if (personnelList.value.length > 0) {
          selectPerson(personnelList.value[0].id);
        }
      }
    } catch (error) {
      console.error("解析数据失败:", error);
    }
  } else {
    console.log("未找到data参数");
  }
};

const selectPerson = (personId: number) => {
  selectedPersonId.value = personId;
  selectedQualificationId.value = "";
  console.log("选择人员:", personId);

  // 默认选择第一个资质类型
  const qualifications = qualificationsMap.value[personId.toString()];
  if (qualifications && qualifications.length > 0) {
    selectQualification(qualifications[0].id);
  }
};

const selectQualification = (qualificationId: string) => {
  selectedQualificationId.value = qualificationId;
  console.log("选择资质类型:", qualificationId);
};

const getPersonTagType = (gongzhong: string) => {
  // 根据工种返回标签类型
  switch (gongzhong) {
    case "8": // 安装人员
      return "warning";
    case "jiazigong": // 架子工
    case "7": // 普通工人
      return "info";
    default:
      return "info";
  }
};

const getWorkTypeName = (gongzhong: string) => {
  // 根据工种代码返回工种名称
  const workTypeMap: Record<string, string> = {
    "jiazigong": "架子工",
    "2": "工种2",
    "7": "普通工人",
    "8": "安装人员",
    "1": "工种1",
    "3": "工种3"
  };
  return workTypeMap[gongzhong] || `工种${gongzhong}`;
};

const goBack = () => {
  router.back();
};

const isImage = (url: string) => {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
};

const isPdf = (url: string) => {
  return /\.pdf$/i.test(url);
};

const downloadFile = (attachment: Attachment) => {
  // 实现文件下载逻辑
  const link = document.createElement("a");
  link.href = attachment.url;
  link.download = attachment.name;
  link.click();
};

const handleConfirm = () => {
  // 确认操作，保存审核意见到本地存储或发送到服务器
  if (reviewComment.value.trim()) {
    console.log("保存审核意见:", reviewComment.value);
    // 这里可以调用API保存审核意见
  }
  goBack();
};

onMounted(() => {
  // 组件挂载后初始化数据
  initializeData();
});
</script>

<style scoped lang="scss">
.personnel-qualification-preview {
  min-height: 100vh;
  background-color: #f5f5f5;

  .page-header {
    background-color: #fff;
    padding: 16px 24px;
    border-bottom: 1px solid #e5e6eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #121314;
    }
  }

  .preview-content {
    display: flex;
    height: 600px;
    border: 1px solid #e5e6eb;

    .left-panel {
      width: 334px;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;
      position: relative;

      .panel-header {
        padding: 16px;
        border-bottom: 1px solid #e5e6eb;

        h3 {
          margin: 0 0 16px 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }

        .search-input {
          :deep(.el-input__wrapper) {
            border-radius: 2px;
          }
        }
      }

      .personnel-list {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;

        .personnel-item {
          padding: 0 16px;
          height: 40px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 2px;
          margin: 0 8px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 2px solid #165dff;
            margin-left: 6px;

            .person-name {
              color: #165dff;
            }
          }

          .person-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .person-name {
              font-size: 14px;
              color: #121314;
            }

            .el-tag {
              border-radius: 34px;
              font-size: 12px;
            }
          }
        }
      }

      .scroll-indicator {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 6px;

        .scroll-track {
          width: 100%;
          height: 100%;
          background-color: #f2f3f5;
          position: relative;

          .scroll-thumb {
            width: 100%;
            background-color: #c9cdd4;
            border-radius: 27px;
            position: absolute;
          }
        }
      }
    }

    .middle-panel {
      flex: 1;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 16px;
        border-bottom: 1px solid #e5e6eb;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }
      }

      .qualification-list {
        flex: 1;
        overflow-y: auto;

        .qualification-item {
          padding: 0 16px;
          height: 40px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 2px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 2px solid #165dff;

            .qualification-name {
              color: #165dff;
            }
          }

          &.required {
            padding-left: 27px;
          }

          .qualification-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .qualification-name {
              font-size: 14px;
              color: #121314;
            }

            .upload-status {
              display: flex;
              align-items: center;
              gap: 4px;

              .status-text {
                font-size: 12px;

                &.uploaded {
                  color: #00b42a;
                }

                &.not-uploaded {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 24px;
      gap: 16px;

      .attachment-preview {
        flex: 1;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        overflow: hidden;

        .preview-area {
          width: 100%;
          height: 100%;

          .image-preview {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;

            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          }

          .pdf-preview {
            width: 100%;
            height: 100%;

            iframe {
              width: 100%;
              height: 100%;
            }
          }

          .file-preview {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 16px;
            color: #909399;

            p {
              margin: 0;
              font-size: 14px;
            }
          }
        }

        .no-attachment {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .review-section {
        .review-header {
          margin-bottom: 8px;

          label {
            font-size: 14px;
            color: #5c5c5c;
          }
        }

        .review-textarea {
          :deep(.el-textarea__inner) {
            min-height: 100px;
            border-radius: 2px;
          }
        }
      }
    }
  }

  .page-footer {
    background-color: #fff;
    padding: 16px 24px;
    border-top: 1px solid #e5e6eb;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
