<template>
  <div class="personnel-qualification-preview">
    <div class="preview-content">
      <!-- 左侧人员选择 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>人员选择</h3>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索"
            class="search-input"
            clearable
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="personnel-list">
          <div
            v-for="person in filteredPersonnelList"
            :key="person.id"
            :class="[
              'personnel-item',
              { active: selectedPersonId === person.id },
            ]"
            @click="selectPerson(person.id)"
          >
            <div class="person-info">
              <span class="person-name">{{ person.renyuanxingming }}</span>
              <el-tag :type="getPersonTagType(person.gongzhong)" size="small">
                {{ getWorkTypeName(person.gongzhong) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间资质类型选择 -->
      <div class="middle-panel">
        <div class="panel-header">
          <h3>类型选择</h3>
        </div>

        <div class="qualification-list">
          <div
            v-for="qualification in currentPersonQualifications"
            :key="qualification.id"
            :class="[
              'qualification-item',
              {
                active: selectedQualificationId == qualification.id,
              },
            ]"
            @click="selectQualification(qualification.id)"
          >
            <div class="qualification-info">
              <span class="qualification-name">
                {{ getQualificationTypeName(qualification.zizhileixing) }}
              </span>
              <div class="upload-status">
                <el-icon
                  v-if="
                    qualification.zizhifujian &&
                    qualification.zizhifujian.state === 'uploaded'
                  "
                  color="#00B42A"
                >
                  <CircleCheck />
                </el-icon>
                <el-icon v-else color="#909399">
                  <CircleClose />
                </el-icon>
                <span
                  :class="[
                    'status-text',
                    qualification.zizhifujian &&
                    qualification.zizhifujian.state === 'uploaded'
                      ? 'uploaded'
                      : 'not-uploaded',
                  ]"
                >
                  {{
                    qualification.zizhifujian &&
                    qualification.zizhifujian.state === "uploaded"
                      ? "已上传"
                      : "未上传"
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧附件预览 -->
      <div class="right-panel">
        <div class="attachment-preview">
          <div v-if="currentAttachment" class="preview-area">
            <!-- 图片预览 -->
            <div v-if="isImage(currentAttachment.url)" class="image-preview">
              <img :src="currentAttachment.url" :alt="currentAttachment.name" />
            </div>

            <!-- PDF预览 -->
            <div v-else-if="isPdf(currentAttachment.url)" class="pdf-preview">
              <iframe :src="currentAttachment.url" frameborder="0"></iframe>
            </div>

            <!-- 其他文件类型 -->
            <div v-else class="file-preview">
              <el-icon size="48"><Document /></el-icon>
              <p>{{ currentAttachment.filename }}</p>
              <p class="file-size">
                文件大小: {{ formatFileSize(currentAttachment.size) }}
              </p>
              <el-button
                type="primary"
                @click="downloadFile(currentAttachment)"
              >
                下载文件
              </el-button>
            </div>
          </div>

          <div v-else class="no-attachment">
            <el-empty description="暂无附件" />
          </div>
        </div>

        <!-- 审核意见 -->
        <div class="review-section">
          <div class="review-header">
            <label>*审核意见</label>
          </div>
          <el-input
            v-model="reviewComment"
            type="textarea"
            :rows="4"
            placeholder="内容"
            class="review-textarea"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import {
  Search,
  CircleCheck,
  CircleClose,
  Document,
} from "@element-plus/icons-vue";

const route = useRoute();

// 接口定义
interface Personnel {
  id: number;
  renyuanxingming: string; // 人员姓名
  gongzhong: string; // 工种
  shoujihaoma: string; // 手机号码
  guishubumen: string; // 归属部门
  yuangongzhuangtai: string; // 员工状态
  qiyemingcheng: string; // 企业名称
  guishubanzu: string; // 归属班组
  zhuanye: string; // 专业
  zizhi_items: ZizhiItem[]; // 资质项目数组
}

interface ZizhiItem {
  id: number;
  renyuanxingming: string;
  zizhileixing: string; // 资质类型
  zizhizhuangtai: number; // 资质状态
  zizhishengxiaoriqi: string; // 生效日期
  zizhishixiaoriqi: string; // 失效日期
  tezhongzuoyecaozuozhengleixing: string; // 特种作业操作证类型
  zizhifujian: ZizhiFujian; // 资质附件
}

interface ZizhiFujian {
  id: string;
  filename: string;
  name: string;
  size: number;
  mimetype: string;
  state: string;
  value: string;
  url: string;
}

// 响应式数据
const searchKeyword = ref("");
const selectedPersonId = ref<number | null>(null);
const selectedQualificationId = ref("");
const reviewComment = ref("");

// 人员数据
const personnelList = ref<Personnel[]>([]);
const selectedZizhiItem = ref<ZizhiItem | null>(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalPersonnel = ref(0);

// 计算属性
const filteredPersonnelList = computed(() => {
  let filtered = personnelList.value;

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(
      (person: Personnel) =>
        person.renyuanxingming.includes(searchKeyword.value) ||
        person.gongzhong.includes(searchKeyword.value)
    );
  }

  // 更新总数
  totalPersonnel.value = filtered.length;

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;

  return filtered.slice(start, end);
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(totalPersonnel.value / pageSize.value);
});

const currentPersonQualifications = computed(() => {
  if (selectedPersonId.value === null) return [];
  const person = personnelList.value.find(
    (p) => p.id === selectedPersonId.value
  );
  return person?.zizhi_items || [];
});

const currentAttachment = computed(() => {
  return selectedZizhiItem.value?.zizhifujian || null;
});

// 路由参数处理
const initializeData = () => {
  const dataParam = route.query.data as string;

  console.log("=== 调试信息 ===");
  console.log("路由参数 route.query:", route.query);
  console.log("data参数:", dataParam);

  if (dataParam) {
    try {
      const parsedData = JSON.parse(decodeURIComponent(dataParam));
      console.log("解析后的数据:", parsedData);

      if (Array.isArray(parsedData)) {
        personnelList.value = parsedData;
        console.log("设置人员列表:", personnelList.value);

        // 默认选择第一个人员
        if (personnelList.value.length > 0) {
          selectPerson(personnelList.value[0].id);
        }
      }
    } catch (error) {
      console.error("解析数据失败:", error);
    }
  } else {
    console.log("未找到data参数");
  }
};

const selectPerson = (personId: number) => {
  selectedPersonId.value = personId;
  selectedQualificationId.value = "";
  selectedZizhiItem.value = null;
  console.log("选择人员:", personId);

  // 默认选择第一个资质项目
  const person = personnelList.value.find((p) => p.id === personId);
  if (person && person.zizhi_items.length > 0) {
    selectQualification(person.zizhi_items[0].id);
  }
};

const selectQualification = (qualificationId: number) => {
  selectedQualificationId.value = qualificationId.toString();
  console.log("选择资质类型:", qualificationId);

  // 找到对应的资质项目
  const person = personnelList.value.find(
    (p) => p.id === selectedPersonId.value
  );
  if (person) {
    selectedZizhiItem.value =
      person.zizhi_items.find((item) => item.id === qualificationId) || null;
  }
};

const getPersonTagType = (gongzhong: string) => {
  // 根据工种返回标签类型
  switch (gongzhong) {
    case "8": // 安装人员
      return "warning";
    case "jiazigong": // 架子工
    case "7": // 普通工人
      return "info";
    default:
      return "info";
  }
};

const getWorkTypeName = (gongzhong: string) => {
  // 根据工种代码返回工种名称
  const workTypeMap: Record<string, string> = {
    jiazigong: "架子工",
    "2": "工种2",
    "7": "普通工人",
    "8": "安装人员",
    "1": "工种1",
    "3": "工种3",
  };
  return workTypeMap[gongzhong] || `工种${gongzhong}`;
};

const getQualificationTypeName = (zizhileixing: string) => {
  // 根据资质类型代码返回资质类型名称
  const qualificationTypeMap: Record<string, string> = {
    "1": "特种作业证",
    "2": "健康证明",
    "3": "三级教育证明",
    "4": "考试成绩证明",
    "5": "其他证明",
    "6": "劳动合同",
    "7": "身份证",
  };
  return qualificationTypeMap[zizhileixing] || `资质类型${zizhileixing}`;
};

const formatFileSize = (bytes: number) => {
  // 格式化文件大小
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const isImage = (url: string) => {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
};

const isPdf = (url: string) => {
  return /\.pdf$/i.test(url);
};

const downloadFile = (attachment: ZizhiFujian) => {
  // 实现文件下载逻辑
  const link = document.createElement("a");
  link.href = attachment.url;
  link.download = attachment.filename;
  link.click();
};

// 暴露审核意见给外部调用
const getReviewComment = () => {
  return reviewComment.value;
};

// 暴露方法给外部调用
defineExpose({
  getReviewComment,
});

onMounted(() => {
  // 组件挂载后初始化数据
  initializeData();
});
</script>

<style scoped lang="scss">
.personnel-qualification-preview {
  width: 100%;
  height: 100%;
  background-color: #fff;

  .preview-content {
    display: flex;
    height: 600px;
    border: 1px solid #e5e6eb;

    .left-panel {
      width: 334px;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;
      position: relative;

      .panel-header {
        padding: 16px;
        border-bottom: 1px solid #e5e6eb;

        h3 {
          margin: 0 0 16px 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }

        .search-input {
          :deep(.el-input__wrapper) {
            border-radius: 2px;
          }
        }
      }

      .personnel-list {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f2f3f5;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c9cdd4;
          border-radius: 3px;

          &:hover {
            background: #a8abb2;
          }
        }

        .personnel-item {
          padding: 0 16px;
          height: 40px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 2px;
          margin: 0 8px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 2px solid #165dff;
            margin-left: 6px;

            .person-name {
              color: #165dff;
            }
          }

          .person-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .person-name {
              font-size: 14px;
              color: #121314;
            }

            .el-tag {
              border-radius: 34px;
              font-size: 12px;
            }
          }
        }
      }
    }

    .middle-panel {
      flex: 1;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 16px;
        border-bottom: 1px solid #e5e6eb;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }
      }

      .qualification-list {
        flex: 1;
        overflow-y: auto;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f2f3f5;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c9cdd4;
          border-radius: 3px;

          &:hover {
            background: #a8abb2;
          }
        }

        .qualification-item {
          padding: 0 16px;
          height: 40px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 2px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 2px solid #165dff;

            .qualification-name {
              color: #165dff;
            }
          }

          &.required {
            padding-left: 27px;
          }

          .qualification-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .qualification-name {
              font-size: 14px;
              color: #121314;
            }

            .upload-status {
              display: flex;
              align-items: center;
              gap: 4px;

              .status-text {
                font-size: 12px;

                &.uploaded {
                  color: #00b42a;
                }

                &.not-uploaded {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 24px;
      gap: 16px;

      .attachment-preview {
        flex: 1;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        overflow: hidden;

        .preview-area {
          width: 100%;
          height: 100%;

          .image-preview {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;

            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          }

          .pdf-preview {
            width: 100%;
            height: 100%;

            iframe {
              width: 100%;
              height: 100%;
            }
          }

          .file-preview {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 16px;
            color: #909399;

            p {
              margin: 0;
              font-size: 14px;
            }

            .file-size {
              font-size: 12px;
              color: #c9cdd4;
            }
          }
        }

        .no-attachment {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .review-section {
        .review-header {
          margin-bottom: 8px;

          label {
            font-size: 14px;
            color: #5c5c5c;
          }
        }

        .review-textarea {
          :deep(.el-textarea__inner) {
            min-height: 100px;
            border-radius: 2px;
          }
        }
      }
    }
  }
}
</style>
