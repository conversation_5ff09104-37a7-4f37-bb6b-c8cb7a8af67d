<template>
  <div class="test-page">
    <div class="test-header">
      <h1>测试页面 - 路由参数传递</h1>
      <p>点击下面的按钮测试路由参数传递功能</p>
    </div>

    <div class="test-content">
      <el-card>
        <template #header>
          <span>测试数据</span>
        </template>

        <div class="test-data">
          <h3>将要传递的数据：</h3>
          <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
        </div>

        <div class="test-buttons">
          <el-button type="primary" size="large" @click="testRouteParams">
            测试路由参数传递
          </el-button>

          <el-button type="success" size="large" @click="testDirectNavigation">
            直接跳转到预览页面
          </el-button>

          <el-button type="warning" size="large" @click="generateMoreData">
            生成更多数据测试分页 (当前: {{ testData.length }}条)
          </el-button>

          <el-button type="info" size="large" @click="resetToMinimalData">
            重置为少量数据 (测试固定分页)
          </el-button>
        </div>

        <div class="test-info">
          <h3>测试说明：</h3>
          <ul>
            <li>1. 点击"测试路由参数传递"会将测试数据作为URL参数传递</li>
            <li>2. 预览页面会接收并解析这些数据</li>
            <li>3. 在浏览器控制台查看调试信息</li>
            <li>4. 数据格式符合您提供的JSON结构</li>
          </ul>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

// 测试数据 - 使用您提供的新数据结构
const testData = ref([
  {
    shoujihaoma: "17333735973",
    zizhi_items: [],
    guishubumen: "广东拓奇电力技术发展有限公司项目部",
    yuangongzhuangtai: "1",
    qiyemingcheng: "广东拓奇电力技术发展有限公司",
    renyuanxingming: "谢谢谢",
    guishubanzu: "广东拓奇电力技术发展有限公司项目部",
    gongzhong: "jiazigong",
    id: 18,
    zhuanye: "1",
  },
  {
    shoujihaoma: "15079073001",
    zizhi_items: [
      {
        shoujihaoma: "15079073001",
        tezhongzuoyecaozuozhengleixing: "",
        zizhishixiaoriqi: "1755792000",
        zizhifujian: {
          filename: "测试文件.pdf",
          size: 198027,
          name: "测试文件.pdf",
          mimetype: "application/pdf",
          state: "uploaded",
          id: "38c370c8d3e7",
          value: "/test/files/test.pdf",
          url: "/test/files/test.pdf",
        },
        zizhileixing: "1",
        zizhishengxiaoriqi: "1755619200",
        zizhizhuangtai: 1,
        renyuanxingming: "测试人员0730-01",
        id: 1,
      },
    ],
    guishubumen: "2096",
    yuangongzhuangtai: "1",
    qiyemingcheng: "广东拓奇电力技术发展有限公司",
    renyuanxingming: "测试人员0730-01",
    gongzhong: "7",
    id: 28,
    zhuanye: "1",
  },
]);

// 生成更多测试数据来测试分页功能
const generateMoreData = () => {
  const additionalData = [];
  for (let i = 3; i <= 20; i++) {
    additionalData.push({
      shoujihaoma: `1733373597${i}`,
      zizhi_items: [],
      guishubumen: `测试部门${i}`,
      yuangongzhuangtai: "1",
      qiyemingcheng: "广东拓奇电力技术发展有限公司",
      renyuanxingming: `测试人员${i}`,
      guishubanzu: `测试班组${i}`,
      gongzhong: i % 3 === 0 ? "jiazigong" : i % 3 === 1 ? "7" : "8",
      id: i + 25,
      zhuanye: "1",
    });
  }
  testData.value = [...testData.value, ...additionalData];
  console.log(
    `生成了${additionalData.length}条额外数据，总计${testData.value.length}条`
  );
};

// 重置为少量数据
const resetToMinimalData = () => {
  testData.value = [
    {
      shoujihaoma: "17333735973",
      zizhi_items: [],
      guishubumen: "广东拓奇电力技术发展有限公司项目部",
      yuangongzhuangtai: "1",
      qiyemingcheng: "广东拓奇电力技术发展有限公司",
      renyuanxingming: "谢谢谢",
      guishubanzu: "广东拓奇电力技术发展有限公司项目部",
      gongzhong: "jiazigong",
      id: 18,
      zhuanye: "1",
    },
    {
      shoujihaoma: "15079073001",
      zizhi_items: [
        {
          shoujihaoma: "15079073001",
          tezhongzuoyecaozuozhengleixing: "",
          zizhishixiaoriqi: "1755792000",
          zizhifujian: {
            filename: "测试文件.pdf",
            size: 198027,
            name: "测试文件.pdf",
            mimetype: "application/pdf",
            state: "uploaded",
            id: "38c370c8d3e7",
            value: "/test/files/test.pdf",
            url: "/test/files/test.pdf",
          },
          zizhileixing: "1",
          zizhishengxiaoriqi: "1755619200",
          zizhizhuangtai: 1,
          renyuanxingming: "测试人员0730-01",
          id: 1,
        },
      ],
      guishubumen: "2096",
      yuangongzhuangtai: "1",
      qiyemingcheng: "广东拓奇电力技术发展有限公司",
      renyuanxingming: "测试人员0730-01",
      gongzhong: "7",
      id: 28,
      zhuanye: "1",
    },
  ];
  console.log(`重置为${testData.value.length}条基础数据`);
};

// 测试路由参数传递
const testRouteParams = () => {
  const dataString = encodeURIComponent(JSON.stringify(testData.value));

  console.log("=== 发送数据 ===");
  console.log("原始数据:", testData.value);
  console.log("编码后的数据:", dataString);

  router.push({
    path: "/personnel-qualification-preview",
    query: {
      data: dataString,
    },
  });
};

// 直接跳转到预览页面
const testDirectNavigation = () => {
  router.push("/personnel-qualification-preview");
};

// 组件挂载时的初始化逻辑
onMounted(() => {
  console.log(
    router.currentRoute.value.query.data,
    router.currentRoute.value.query.host
  );
  testData.value = JSON.parse(router.currentRoute.value.query.data as string);
  testData.value.forEach((item) => {
    item.zizhi_items.forEach((item) => {
      item.zizhifujian.url = "http://************:8090" + item.zizhifujian.url;
      item.zizhifujian.value =
        "http://************:8090" + item.zizhifujian.value;
    });
  });
  console.log(testData.value);
});
</script>

<style scoped lang="scss">
.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      color: #121314;
      margin-bottom: 10px;
      font-size: 28px;
    }

    p {
      color: #909399;
      font-size: 16px;
    }
  }

  .test-content {
    max-width: 800px;
    margin: 0 auto;

    .el-card {
      :deep(.el-card__header) {
        background-color: #fafafa;

        span {
          font-weight: 600;
          color: #121314;
          font-size: 16px;
        }
      }

      .test-data {
        margin-bottom: 30px;

        h3 {
          color: #121314;
          margin-bottom: 10px;
          font-size: 16px;
        }

        pre {
          background-color: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 16px;
          font-size: 12px;
          color: #495057;
          overflow-x: auto;
          max-height: 300px;
        }
      }

      .test-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 30px;

        .el-button {
          padding: 12px 24px;
          font-size: 16px;
        }
      }

      .test-info {
        h3 {
          color: #121314;
          margin-bottom: 10px;
          font-size: 16px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            color: #606266;
            line-height: 1.6;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    padding: 10px;

    .test-content {
      .el-card {
        .test-buttons {
          flex-direction: column;
          align-items: center;

          .el-button {
            width: 200px;
          }
        }
      }
    }
  }
}
</style>
