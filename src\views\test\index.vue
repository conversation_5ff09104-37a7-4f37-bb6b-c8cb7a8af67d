<template>
  <div class="test-page">
    <div class="test-header">
      <h1>测试页面 - 路由参数传递</h1>
      <p>点击下面的按钮测试路由参数传递功能</p>
    </div>

    <div class="test-content">
      <el-card>
        <template #header>
          <span>测试数据</span>
        </template>
        
        <div class="test-data">
          <h3>将要传递的数据：</h3>
          <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
        </div>

        <div class="test-buttons">
          <el-button type="primary" size="large" @click="testRouteParams">
            测试路由参数传递
          </el-button>
          
          <el-button type="success" size="large" @click="testDirectNavigation">
            直接跳转到预览页面
          </el-button>
        </div>

        <div class="test-info">
          <h3>测试说明：</h3>
          <ul>
            <li>1. 点击"测试路由参数传递"会将测试数据作为URL参数传递</li>
            <li>2. 预览页面会接收并解析这些数据</li>
            <li>3. 在浏览器控制台查看调试信息</li>
            <li>4. 数据格式符合您提供的JSON结构</li>
          </ul>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 测试数据 - 使用您提供的数据结构
const testData = ref([
  {
    "shoujihaoma": "17333735973",
    "guishubumen": "广东拓奇电力技术发展有限公司项目部",
    "yuangongzhuangtai": "1",
    "qiyemingcheng": "广东拓奇电力技术发展有限公司",
    "renyuanxingming": "谢谢谢",
    "guishubanzu": "广东拓奇电力技术发展有限公司项目部",
    "gongzhong": "jiazigong",
    "id": 18,
    "zhuanye": "1"
  },
  {
    "shoujihaoma": "17333735973",
    "guishubumen": "2099",
    "yuangongzhuangtai": "1",
    "qiyemingcheng": "广东拓奇电力技术发展有限公司",
    "renyuanxingming": "谢谢谢",
    "guishubanzu": "94293",
    "gongzhong": "2",
    "id": 23,
    "zhuanye": "1"
  },
  {
    "shoujihaoma": "17333735976",
    "guishubumen": "2100",
    "yuangongzhuangtai": "1",
    "qiyemingcheng": "广东拓奇电力技术发展有限公司",
    "renyuanxingming": "研究工程师",
    "guishubanzu": "2121",
    "gongzhong": "8",
    "id": 27,
    "zhuanye": "3"
  },
  {
    "shoujihaoma": "15079073001",
    "guishubumen": "2096",
    "yuangongzhuangtai": "1",
    "qiyemingcheng": "广东拓奇电力技术发展有限公司",
    "renyuanxingming": "测试人员0730-01",
    "gongzhong": "7",
    "id": 28,
    "zhuanye": "1"
  }
])

// 测试路由参数传递
const testRouteParams = () => {
  const dataString = encodeURIComponent(JSON.stringify(testData.value))
  
  console.log("=== 发送数据 ===")
  console.log("原始数据:", testData.value)
  console.log("编码后的数据:", dataString)
  
  router.push({
    path: '/personnel-qualification-preview',
    query: {
      data: dataString
    }
  })
}

// 直接跳转到预览页面
const testDirectNavigation = () => {
  router.push('/personnel-qualification-preview')
}
</script>

<style scoped lang="scss">
.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      color: #121314;
      margin-bottom: 10px;
      font-size: 28px;
    }

    p {
      color: #909399;
      font-size: 16px;
    }
  }

  .test-content {
    max-width: 800px;
    margin: 0 auto;

    .el-card {
      :deep(.el-card__header) {
        background-color: #fafafa;
        
        span {
          font-weight: 600;
          color: #121314;
          font-size: 16px;
        }
      }

      .test-data {
        margin-bottom: 30px;

        h3 {
          color: #121314;
          margin-bottom: 10px;
          font-size: 16px;
        }

        pre {
          background-color: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 16px;
          font-size: 12px;
          color: #495057;
          overflow-x: auto;
          max-height: 300px;
        }
      }

      .test-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 30px;

        .el-button {
          padding: 12px 24px;
          font-size: 16px;
        }
      }

      .test-info {
        h3 {
          color: #121314;
          margin-bottom: 10px;
          font-size: 16px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            color: #606266;
            line-height: 1.6;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    padding: 10px;

    .test-content {
      .el-card {
        .test-buttons {
          flex-direction: column;
          align-items: center;

          .el-button {
            width: 200px;
          }
        }
      }
    }
  }
}
</style>
