/**
 * 文件预览API
 * 用于解决CORS跨域问题的文件预览接口
 */
import { request } from './index';

/**
 * 从完整URL中提取S3路径
 * @param fullUrl 完整的文件URL
 * @returns 提取后的S3路径，不包含s3前缀
 * 
 * 示例：
 * 输入: http://172.20.18.72:8090/object/3gZabmbEVL/s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf
 * 输出: /tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf
 */
export function extractS3Path(fullUrl: string): string {
  if (!fullUrl) return '';
  
  try {
    // 查找s3/的位置
    const s3Index = fullUrl.indexOf('/s3/');
    if (s3Index === -1) {
      console.warn('URL中未找到s3路径:', fullUrl);
      return fullUrl; // 如果没有找到s3，返回原URL作为fallback
    }
    
    // 提取s3/之后的路径，并在前面加上/
    const s3Path = fullUrl.substring(s3Index + 3); // +3 是为了跳过 '/s3'
    return s3Path.startsWith('/') ? s3Path : '/' + s3Path;
  } catch (error) {
    console.error('提取S3路径失败:', error, fullUrl);
    return fullUrl;
  }
}

/**
 * 获取文件预览数据（ArrayBuffer格式）
 * @param s3Path S3文件路径
 * @returns Promise<ArrayBuffer> 文件的二进制数据
 */
export async function getFilePreviewBuffer(s3Path: string): Promise<ArrayBuffer> {
  try {
    console.log('请求文件预览:', s3Path);

    const response = await request.request({
      url: '/file/preview', // 根据实际后端接口调整
      method: 'POST',
      data: {
        filePath: s3Path
      },
      responseType: 'arraybuffer', // 关键：指定响应类型为arraybuffer
      requestOptions: {
        isTransformResponse: false, // 不进行数据转换
        isReturnNativeResponse: true, // 返回原生响应
      }
    });

    return response.data;
  } catch (error) {
    console.error('获取文件预览失败:', error);
    throw new Error(`文件预览失败: ${error}`);
  }
}

/**
 * 通过模拟下载方式获取文件ArrayBuffer
 * @param fullUrl 完整的文件URL
 * @returns Promise<ArrayBuffer> 文件的二进制数据
 */
export async function downloadFileAsArrayBuffer(fullUrl: string): Promise<ArrayBuffer> {
  try {
    console.log('模拟下载文件获取ArrayBuffer:', fullUrl);

    const response = await fetch(fullUrl, {
      method: 'GET',
      mode: 'cors', // 尝试CORS请求
      credentials: 'omit', // 不发送凭据
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    console.log('文件下载成功，大小:', arrayBuffer.byteLength, 'bytes');

    return arrayBuffer;
  } catch (error) {
    console.error('模拟下载文件失败:', error);
    throw new Error(`文件下载失败: ${error}`);
  }
}

/**
 * 获取文件预览的Blob URL
 * @param s3Path S3文件路径
 * @param mimeType 文件MIME类型，默认为application/pdf
 * @returns Promise<string> Blob URL
 */
export async function getFilePreviewBlobUrl(s3Path: string, mimeType: string = 'application/pdf'): Promise<string> {
  try {
    const arrayBuffer = await getFilePreviewBuffer(s3Path);
    const blob = new Blob([arrayBuffer], { type: mimeType });
    const blobUrl = URL.createObjectURL(blob);
    
    console.log('生成Blob URL成功:', blobUrl);
    return blobUrl;
  } catch (error) {
    console.error('生成Blob URL失败:', error);
    throw error;
  }
}

/**
 * 清理Blob URL
 * @param blobUrl 要清理的Blob URL
 */
export function revokeBlobUrl(blobUrl: string): void {
  if (blobUrl && blobUrl.startsWith('blob:')) {
    URL.revokeObjectURL(blobUrl);
    console.log('Blob URL已清理:', blobUrl);
  }
}

/**
 * 检查是否为需要特殊处理的文件URL
 * @param url 文件URL
 * @returns boolean 是否需要通过API预览
 */
export function needsApiPreview(url: string): boolean {
  if (!url) return false;
  
  // 检查是否包含跨域的文件服务器URL
  const crossOriginPatterns = [
    /^https?:\/\/172\.20\.18\.72:8090/,
    /\/s3\//,
    /\/object\//
  ];
  
  return crossOriginPatterns.some(pattern => pattern.test(url));
}

/**
 * 文件预览管理器
 * 用于管理Blob URL的生命周期
 */
export class FilePreviewManager {
  private blobUrls: Set<string> = new Set();
  
  /**
   * 创建文件预览URL
   * @param fullUrl 完整的文件URL
   * @param mimeType 文件MIME类型
   * @returns Promise<string> 预览URL
   */
  async createPreviewUrl(fullUrl: string, mimeType: string = 'application/pdf'): Promise<string> {
    try {
      if (!needsApiPreview(fullUrl)) {
        // 如果不需要API预览，直接返回原URL
        return fullUrl;
      }

      const s3Path = extractS3Path(fullUrl);
      const blobUrl = await getFilePreviewBlobUrl(s3Path, mimeType);

      // 记录生成的Blob URL，用于后续清理
      this.blobUrls.add(blobUrl);

      return blobUrl;
    } catch (error) {
      console.error('创建预览URL失败，使用原URL:', error);
      return fullUrl; // fallback到原URL
    }
  }

  /**
   * 通过下载方式创建文件预览URL
   * @param fullUrl 完整的文件URL
   * @param mimeType 文件MIME类型
   * @returns Promise<string> 预览URL
   */
  async createPreviewUrlByDownload(fullUrl: string, mimeType: string = 'application/pdf'): Promise<string> {
    try {
      console.log('通过下载方式创建预览URL:', fullUrl);

      // 直接下载文件获取ArrayBuffer
      const arrayBuffer = await downloadFileAsArrayBuffer(fullUrl);

      // 创建Blob和Blob URL
      const blob = new Blob([arrayBuffer], { type: mimeType });
      const blobUrl = URL.createObjectURL(blob);

      // 记录生成的Blob URL，用于后续清理
      this.blobUrls.add(blobUrl);

      console.log('通过下载方式生成Blob URL成功:', blobUrl);
      return blobUrl;
    } catch (error) {
      console.error('通过下载方式创建预览URL失败:', error);
      throw error; // 不fallback，让调用方处理
    }
  }
  
  /**
   * 清理所有Blob URL
   */
  cleanup(): void {
    this.blobUrls.forEach(url => {
      revokeBlobUrl(url);
    });
    this.blobUrls.clear();
    console.log('所有Blob URL已清理');
  }
  
  /**
   * 清理特定的Blob URL
   * @param url 要清理的URL
   */
  cleanupUrl(url: string): void {
    if (this.blobUrls.has(url)) {
      revokeBlobUrl(url);
      this.blobUrls.delete(url);
    }
  }
}

// 导出单例实例
export const filePreviewManager = new FilePreviewManager();
