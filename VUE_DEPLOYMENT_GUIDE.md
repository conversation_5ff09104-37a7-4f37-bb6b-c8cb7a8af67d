# Vue项目部署配置指南

## 🎯 部署环境分析

### 项目信息
- **项目名称**: suda-embedded（速搭嵌入式系统）
- **开发环境**: http://localhost:8853
- **集成需求**: 在速搭平台的iframe中正常访问和通信
- **当前状态**: 本地开发完成，需要部署到生产环境

## 🤔 部署方式选择

### 方案对比

| 部署方式 | 优势 | 劣势 | 推荐度 |
|---------|------|------|--------|
| **同服务器部署** | 无跨域问题、通信简单 | 耦合度高、维护复杂 | ⭐⭐⭐ |
| **独立服务器+Nginx** | 解耦、易维护、可扩展 | 需要处理跨域 | ⭐⭐⭐⭐⭐ |
| **CDN部署** | 性能最佳、全球加速 | 配置复杂、成本较高 | ⭐⭐⭐⭐ |

### 推荐方案：独立服务器+Nginx部署

**原因**：
1. **解耦合**: Vue项目独立部署，便于维护和更新
2. **可扩展**: 支持负载均衡、多实例部署
3. **跨域可控**: 通过Nginx配置解决跨域问题
4. **性能优化**: Nginx静态文件服务性能优异

## 🚀 部署实施方案

### 1. 项目构建配置

#### vite.config.ts 配置
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  base: '/', // 根据实际部署路径调整
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // 生产环境关闭sourcemap
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          elementPlus: ['element-plus'],
          utils: ['axios']
        }
      }
    }
  },
  server: {
    port: 8853,
    host: '0.0.0.0',
    cors: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})
```

#### package.json 构建脚本
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "build:prod": "vue-tsc && vite build --mode production",
    "preview": "vite preview --port 8853",
    "deploy": "npm run build:prod && npm run upload"
  }
}
```

### 2. Nginx配置方案

#### 完整的nginx.conf配置
```nginx
# /etc/nginx/sites-available/suda-embedded
server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;  # 替换为您的域名
    
    # SSL配置（推荐使用HTTPS）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 跨域配置（关键配置）
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
    add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
    
    # 处理预检请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type 'text/plain; charset=utf-8';
        add_header Content-Length 0;
        return 204;
    }
    
    # 静态文件根目录
    root /var/www/suda-embedded/dist;
    index index.html;
    
    # 主应用路由
    location / {
        try_files $uri $uri/ /index.html;
        
        # 缓存策略
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        location ~* \.(html)$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # API代理（如果需要）
    location /api/ {
        proxy_pass http://your-backend-server/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 日志配置
    access_log /var/log/nginx/suda-embedded.access.log;
    error_log /var/log/nginx/suda-embedded.error.log;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

#### 简化版nginx配置（快速部署）
```nginx
server {
    listen 8853;
    server_name localhost;
    
    # 跨域配置
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
    
    root /var/www/suda-embedded/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public";
    }
}
```

### 3. 部署脚本

#### deploy.sh 自动化部署脚本
```bash
#!/bin/bash

# 部署配置
PROJECT_NAME="suda-embedded"
BUILD_DIR="dist"
DEPLOY_DIR="/var/www/$PROJECT_NAME"
NGINX_CONFIG="/etc/nginx/sites-available/$PROJECT_NAME"
BACKUP_DIR="/var/backups/$PROJECT_NAME"

echo "🚀 开始部署 $PROJECT_NAME..."

# 1. 构建项目
echo "📦 构建项目..."
npm run build:prod

if [ $? -ne 0 ]; then
    echo "❌ 构建失败！"
    exit 1
fi

# 2. 备份当前版本
if [ -d "$DEPLOY_DIR" ]; then
    echo "💾 备份当前版本..."
    sudo mkdir -p $BACKUP_DIR
    sudo cp -r $DEPLOY_DIR $BACKUP_DIR/$(date +%Y%m%d_%H%M%S)
fi

# 3. 部署新版本
echo "🔄 部署新版本..."
sudo mkdir -p $DEPLOY_DIR
sudo cp -r $BUILD_DIR/* $DEPLOY_DIR/
sudo chown -R www-data:www-data $DEPLOY_DIR
sudo chmod -R 755 $DEPLOY_DIR

# 4. 更新Nginx配置
echo "⚙️ 更新Nginx配置..."
sudo cp nginx.conf $NGINX_CONFIG
sudo nginx -t

if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "✅ Nginx配置更新成功！"
else
    echo "❌ Nginx配置错误！"
    exit 1
fi

# 5. 健康检查
echo "🔍 健康检查..."
sleep 2
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8853/health)

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 部署成功！应用正常运行"
    echo "🌐 访问地址: http://your-domain.com:8853"
else
    echo "❌ 健康检查失败，HTTP状态码: $HTTP_CODE"
    exit 1
fi

echo "🎉 部署完成！"
```

### 4. Docker部署方案（可选）

#### Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build:prod

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### docker-compose.yml
```yaml
version: '3.8'
services:
  suda-embedded:
    build: .
    ports:
      - "8853:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/nginx
```

## 🔧 跨域问题解决

### 1. Nginx CORS配置
已在上述Nginx配置中包含完整的CORS设置。

### 2. 应用内配置
```typescript
// src/utils/request.ts
import axios from 'axios'

const request = axios.create({
  baseURL: process.env.NODE_ENV === 'production' 
    ? 'https://your-api-domain.com' 
    : 'http://localhost:3000',
  timeout: 10000,
  withCredentials: true // 如果需要携带cookie
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加必要的请求头
    config.headers['Content-Type'] = 'application/json'
    return config
  },
  error => Promise.reject(error)
)

export default request
```

### 3. 速搭平台iframe配置
```html
<!-- 在速搭平台中使用iframe时 -->
<iframe 
  src="https://your-domain.com:8853/personnel-qualification-preview"
  sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
  style="width: 100%; height: 100%;"
></iframe>
```

## 📋 部署检查清单

### 部署前检查
- [ ] 项目构建成功
- [ ] 环境变量配置正确
- [ ] API接口地址更新
- [ ] 静态资源路径正确

### 部署后检查
- [ ] 应用可以正常访问
- [ ] iframe嵌入功能正常
- [ ] postMessage通信正常
- [ ] 文件上传下载功能正常
- [ ] 跨域配置生效

### 性能优化检查
- [ ] 静态资源缓存配置
- [ ] Gzip压缩启用
- [ ] 图片资源优化
- [ ] 代码分割生效

## 🎯 推荐部署流程

1. **开发环境测试** → 确保所有功能正常
2. **构建生产版本** → `npm run build:prod`
3. **准备服务器环境** → 安装Nginx、配置域名
4. **部署应用文件** → 上传dist目录到服务器
5. **配置Nginx** → 应用上述配置文件
6. **测试验证** → 验证所有功能正常
7. **监控运维** → 设置日志监控和备份

现在您可以根据实际情况选择合适的部署方案。推荐使用独立服务器+Nginx的方式，既保证了性能，又解决了跨域问题！
