# 直接数据初始化和枚举映射实现

## 🎯 功能概述

预览页面现在支持多种数据传递方式，并添加了完整的工种和资质类型枚举映射：

1. **直接数据传递** - 通过组件方法或消息传递 ⭐ 新增
2. **路径参数传递** - URL路径参数方式
3. **查询参数传递** - URL查询参数方式
4. **完整枚举映射** - 工种和资质类型的标准化显示

## 🔧 核心实现

### 1. 工种枚举映射
```javascript
// 完整的工种枚举 (111个工种)
const workTypeOptions = [
  { label: "普工", value: "1" },
  { label: "电工", value: "2" },
  { label: "架子工", value: "3" },
  { label: "焊工", value: "4" },
  // ... 107个其他工种
  { label: "机务", value: "111" }
];

const getWorkTypeName = (gongzhong: string) => {
  // 兼容旧的字符串格式
  if (gongzhong === "jiazigong") return "架子工";
  
  // 根据工种代码返回工种名称
  const workType = workTypeOptions.find(item => item.value === gongzhong);
  return workType ? workType.label : `工种${gongzhong}`;
};
```

### 2. 资质类型枚举映射
```javascript
// 人员资质类型枚举
const qualificationTypeOptions = [
  { label: "身份证_国徽面", value: "1" },
  { label: "身份证_人像面", value: "2" },
  { label: "劳动合同", value: "3" },
  { label: "保险", value: "4" },
  { label: "体检报告", value: "5" },
  { label: "特种作业操作证", value: "6" }
];

const getQualificationTypeName = (zizhileixing: string) => {
  const qualificationType = qualificationTypeOptions.find(item => item.value === zizhileixing);
  return qualificationType ? qualificationType.label : `资质类型${zizhileixing}`;
};
```

### 3. 直接数据初始化
```javascript
// 数据初始化处理 - 支持直接传入数据
const initializeData = (data?: Personnel[]) => {
  console.log("=== 调试信息 ===");
  
  // 如果直接传入数据，优先使用
  if (data && Array.isArray(data)) {
    personnelList.value = data;
    console.log("直接接收数据:", data);
  } else {
    // 否则从路由参数获取
    // ... 路由参数处理逻辑
  }
  
  // 重置分页和选择第一个人员
  currentPage.value = 1;
  if (personnelList.value.length > 0) {
    selectPerson(personnelList.value[0].id);
  }
};

// 直接设置数据的方法
const setPersonnelData = (data: Personnel[]) => {
  console.log("直接设置人员数据:", data);
  initializeData(data);
};
```

### 4. 消息监听增强
```javascript
const handleParentMessage = (event: MessageEvent) => {
  if (event.data && event.data.type) {
    switch (event.data.type) {
      case "setPersonnelData":
        // 直接设置人员数据 ⭐ 新增
        if (event.data.data && Array.isArray(event.data.data)) {
          console.log("直接设置人员数据:", event.data.data);
          initializeData(event.data.data);
        }
        break;
      // ... 其他消息类型
    }
  }
};
```

## 🚀 使用方法

### 1. 直接组件方法调用
```javascript
// 获取组件引用
const previewRef = ref();

// 直接设置数据
previewRef.value?.setPersonnelData(personnelData);

// 或者通过初始化方法
previewRef.value?.initializeData(personnelData);
```

### 2. 消息传递方式
```javascript
// 通过postMessage传递数据
window.postMessage({
  type: 'setPersonnelData',
  data: personnelData,
  source: 'parent-component'
}, '*');
```

### 3. 路径参数方式 (保持兼容)
```javascript
const dataString = encodeURIComponent(JSON.stringify(personnelData));
router.push(`/personnel-qualification-preview/${dataString}`);
```

### 4. 查询参数方式 (保持兼容)
```javascript
const dataString = encodeURIComponent(JSON.stringify(personnelData));
router.push(`/personnel-qualification-preview?data=${dataString}`);
```

## 🧪 测试功能

### 测试页面新增功能
访问 http://localhost:5173/test，新增了：
- **测试直接数据传递** 按钮 ⭐

### 测试流程
1. 点击"测试直接数据传递"
2. 页面跳转到预览页面
3. 通过postMessage传递数据
4. 预览页面接收并显示数据

## 📊 数据传递优先级

```
1. 直接传入数据 (最高优先级)
   ↓
2. 路径参数 (route.params.data)
   ↓
3. 查询参数 (route.query.data)
   ↓
4. 空数据状态
```

## 🎨 枚举映射效果

### 工种显示优化
- **原来**: "1" → "工种1"
- **现在**: "1" → "普工"
- **兼容**: "jiazigong" → "架子工"

### 资质类型显示优化
- **原来**: "1" → "资质类型1"
- **现在**: "1" → "身份证_国徽面"

## 📋 暴露的方法

```javascript
defineExpose({
  getReviewComment,      // 获取审核意见
  sendMessageToParent,   // 发送消息给父页面
  setPersonnelData,      // 直接设置人员数据 ⭐ 新增
  initializeData         // 初始化数据 ⭐ 新增
});
```

## ✅ 完成功能

- [x] 直接数据传递支持
- [x] 完整工种枚举映射 (111个工种)
- [x] 资质类型枚举映射 (6种类型)
- [x] 消息监听增强
- [x] 组件方法暴露
- [x] 向后兼容保持
- [x] 测试功能完善

## 🔍 调试信息

预览页面会输出详细的调试信息：
```
=== 调试信息 ===
直接接收数据: [...]
最终人员列表: [...]
选择人员: 18
工种映射: "3" → "架子工"
资质类型映射: "1" → "身份证_国徽面"
```

## 🎉 使用建议

### 推荐使用场景
- **直接数据传递**: 适合组件内部调用，性能最佳
- **消息传递**: 适合跨组件通信，灵活性高
- **路径参数**: 适合RESTful风格，URL简洁
- **查询参数**: 适合传统Web应用，兼容性好

### 最佳实践
1. 优先使用直接数据传递，避免URL编码解码
2. 利用枚举映射提供用户友好的显示
3. 保持向后兼容，支持多种传递方式
4. 通过调试信息验证数据传递过程

现在预览页面支持灵活的数据初始化方式，并提供标准化的枚举显示！
