# 新数据结构优化更新

## 🔄 主要变更

### 1. 数据结构适配
根据您提供的新数据结构，完全重构了资质预览页面的数据处理逻辑。

#### 新的数据结构
```typescript
interface Personnel {
  id: number;
  renyuanxingming: string; // 人员姓名
  gongzhong: string; // 工种
  shoujihaoma: string; // 手机号码
  guishubumen: string; // 归属部门
  yuangongzhuangtai: string; // 员工状态
  qiyemingcheng: string; // 企业名称
  guishubanzu: string; // 归属班组
  zhuanye: string; // 专业
  zizhi_items: ZizhiItem[]; // 资质项目数组 ⭐ 新增
}

interface ZizhiItem {
  id: number;
  renyuanxingming: string;
  zizhileixing: string; // 资质类型
  zizhizhuangtai: number; // 资质状态
  zizhishengxiaoriqi: string; // 生效日期
  zizhishixiaoriqi: string; // 失效日期
  tezhongzuoyecaozuozhengleixing: string; // 特种作业操作证类型
  zizhifujian: ZizhiFujian; // 资质附件 ⭐ 新增
}

interface ZizhiFujian {
  id: string;
  filename: string; // 文件名
  name: string;
  size: number; // 文件大小 ⭐ 新增
  mimetype: string; // MIME类型 ⭐ 新增
  state: string; // 上传状态 ⭐ 新增
  value: string;
  url: string;
}
```

### 2. 功能优化

#### 资质类型显示
- **原来**: 使用固定的资质类型名称
- **现在**: 根据 `zizhileixing` 字段动态映射资质类型名称
- **映射关系**:
  - `"1"` → 特种作业证
  - `"2"` → 健康证明
  - `"3"` → 三级教育证明
  - `"4"` → 考试成绩证明
  - `"5"` → 其他证明
  - `"6"` → 劳动合同
  - `"7"` → 身份证

#### 上传状态判断
- **原来**: 使用 `uploaded` 布尔值
- **现在**: 根据 `zizhifujian.state === 'uploaded'` 判断
- **状态显示**: 
  - ✅ 已上传 (绿色)
  - ❌ 未上传 (灰色)

#### 附件预览增强
- **文件名显示**: 使用 `zizhifujian.filename`
- **文件大小**: 新增文件大小显示，自动格式化 (Bytes/KB/MB/GB)
- **MIME类型**: 支持根据 `mimetype` 判断文件类型
- **下载功能**: 优化下载逻辑，使用正确的文件名

### 3. 新增功能

#### 文件大小格式化
```javascript
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
```

#### 资质类型映射
```javascript
const getQualificationTypeName = (zizhileixing: string) => {
  const qualificationTypeMap = {
    "1": "特种作业证",
    "2": "健康证明",
    "3": "三级教育证明",
    "4": "考试成绩证明",
    "5": "其他证明",
    "6": "劳动合同",
    "7": "身份证",
  };
  return qualificationTypeMap[zizhileixing] || `资质类型${zizhileixing}`;
};
```

### 4. 数据流程优化

#### 选择逻辑
1. **选择人员** → 加载该人员的 `zizhi_items` 数组
2. **选择资质** → 显示对应的 `zizhifujian` 附件信息
3. **附件预览** → 根据文件类型和URL显示预览

#### 测试数据更新
更新了测试页面的数据，包含真实的资质项目和附件信息：
```javascript
{
  "renyuanxingming": "测试人员0730-01",
  "zizhi_items": [
    {
      "id": 1,
      "zizhileixing": "1", // 特种作业证
      "zizhifujian": {
        "filename": "测试文件.pdf",
        "size": 198027,
        "state": "uploaded",
        "url": "/test/files/test.pdf"
      }
    }
  ]
}
```

## 🧪 测试验证

### 测试步骤
1. 启动项目: `npm run dev`
2. 访问测试页面: http://localhost:5173/test
3. 点击"测试路由参数传递"
4. 验证以下功能:
   - ✅ 人员列表正确显示
   - ✅ 资质类型正确映射
   - ✅ 上传状态正确显示
   - ✅ 附件预览正常工作
   - ✅ 文件大小正确格式化

### 调试信息
控制台会输出详细的调试信息：
```
=== 调试信息 ===
路由参数 route.query: {...}
解析后的数据: [{id: 18, zizhi_items: []}, {id: 28, zizhi_items: [...]}]
选择人员: 28
选择资质类型: 1
```

## 🎯 使用示例

### 在低代码平台中使用
```javascript
// 构建包含资质数据的URL
const personnelData = [
  {
    id: 28,
    renyuanxingming: "测试人员",
    gongzhong: "7",
    zizhi_items: [
      {
        id: 1,
        zizhileixing: "1",
        zizhifujian: {
          filename: "特种作业证.pdf",
          size: 198027,
          state: "uploaded",
          url: "/files/cert.pdf"
        }
      }
    ]
  }
];

const encodedData = encodeURIComponent(JSON.stringify(personnelData));
const iframeUrl = `/personnel-qualification-preview?data=${encodedData}`;
```

## ✅ 完成状态

- ✅ 适配新的数据结构
- ✅ 资质类型动态映射
- ✅ 上传状态正确判断
- ✅ 附件预览功能完善
- ✅ 文件大小格式化
- ✅ 测试数据更新
- ✅ 调试信息完整

现在预览页面完全支持您提供的新数据结构，可以正确显示资质项目和附件信息！
