# 预览页面功能增强

## 🎯 功能概述

为personnelQualificationPreview页面添加了两个重要的预览功能增强：

1. **PDF预览功能** - 使用vue-office-pdf组件替代iframe
2. **图片点击放大预览** - 支持图片点击放大查看和下载

## 🔧 主要修改

### 1. PDF预览功能升级

#### 依赖引入
```javascript
import VueOfficePdf from "@vue-office/pdf";
import "@vue-office/pdf/lib/index.css";
```

#### 模板更新
```html
<!-- 原来的iframe方式 -->
<iframe :src="getFullUrl(currentAttachment.url)" frameborder="0"></iframe>

<!-- 新的vue-office-pdf方式 -->
<vue-office-pdf
  :src="getFullUrl(currentAttachment.url)"
  style="width: 100%; height: 100%;"
  @rendered="onPdfRendered"
  @error="onPdfError"
/>
```

#### PDF事件处理
```javascript
// PDF预览相关方法
const onPdfRendered = () => {
  console.log("PDF渲染成功");
};

const onPdfError = (error: any) => {
  console.error("PDF渲染失败:", error);
};
```

### 2. 图片点击放大预览功能

#### 响应式数据
```javascript
// 图片预览相关
const imagePreviewVisible = ref(false);
const imagePreviewUrl = ref("");
const imagePreviewTitle = ref("");
```

#### 图片容器增强
```html
<div class="image-container">
  <img
    :src="getFullUrl(currentAttachment.url)"
    :alt="currentAttachment.filename"
    @click="openImagePreview(getFullUrl(currentAttachment.url), currentAttachment.filename)"
  />
  <div class="image-overlay">
    <el-icon class="zoom-icon" @click="openImagePreview(...)">
      <ZoomIn />
    </el-icon>
  </div>
</div>
```

#### 预览模态框
```html
<el-dialog
  v-model="imagePreviewVisible"
  :title="imagePreviewTitle"
  width="80%"
  :before-close="closeImagePreview"
  class="image-preview-dialog"
>
  <div class="image-preview-content">
    <img
      :src="imagePreviewUrl"
      :alt="imagePreviewTitle"
      class="preview-image"
    />
  </div>
  <template #footer>
    <el-button @click="closeImagePreview">关闭</el-button>
    <el-button type="primary" @click="downloadCurrentImage">下载图片</el-button>
  </template>
</el-dialog>
```

#### 图片预览方法
```javascript
// 图片预览相关方法
const openImagePreview = (url: string, title: string) => {
  imagePreviewUrl.value = url;
  imagePreviewTitle.value = title;
  imagePreviewVisible.value = true;
};

const closeImagePreview = () => {
  imagePreviewVisible.value = false;
  imagePreviewUrl.value = "";
  imagePreviewTitle.value = "";
};

const downloadCurrentImage = () => {
  if (imagePreviewUrl.value) {
    const link = document.createElement("a");
    link.href = imagePreviewUrl.value;
    link.download = imagePreviewTitle.value || "image";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};
```

## 🎨 样式增强

### 图片容器样式
```scss
.image-container {
  position: relative;
  display: inline-block;
  cursor: pointer;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .zoom-icon {
      color: white;
      font-size: 24px;
      cursor: pointer;
    }
  }

  &:hover {
    .image-overlay {
      opacity: 1;
    }

    img {
      transform: scale(1.05);
    }
  }
}
```

### 预览对话框样式
```scss
:deep(.image-preview-dialog) {
  .el-dialog__body {
    padding: 0;
  }

  .image-preview-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background-color: #000;

    .preview-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
    }
  }
}
```

## 🚀 功能特性

### PDF预览优势
- **更好的渲染效果**: vue-office-pdf提供更好的PDF渲染质量
- **事件监听**: 支持渲染成功和错误事件处理
- **更好的兼容性**: 避免iframe的跨域和安全限制
- **响应式设计**: 自适应容器大小

### 图片预览特性
- **悬停效果**: 鼠标悬停显示放大图标和缩放效果
- **点击放大**: 点击图片或放大图标打开大图预览
- **全屏查看**: 模态框中以黑色背景显示大图
- **下载功能**: 支持直接下载当前预览的图片
- **响应式**: 图片自适应屏幕大小，最大高度70vh

## 🧪 测试功能

### 测试步骤
1. 访问预览页面
2. 选择包含图片附件的人员
3. 测试图片悬停效果
4. 点击图片或放大图标测试放大预览
5. 测试图片下载功能
6. 选择包含PDF附件的人员
7. 测试PDF预览功能

### 支持的格式
- **图片格式**: jpg, jpeg, png, gif, bmp, webp
- **PDF格式**: pdf文件
- **其他格式**: 显示文件信息和下载按钮

## 📊 功能对比

### PDF预览对比
```
修复前:
❌ 使用iframe，可能有跨域问题
❌ 渲染效果一般
❌ 缺少错误处理

修复后:
✅ 使用vue-office-pdf，渲染效果更好
✅ 支持事件监听和错误处理
✅ 更好的兼容性和响应式设计
```

### 图片预览对比
```
修复前:
❌ 只能在小窗口中查看
❌ 无法放大查看细节
❌ 缺少交互效果

修复后:
✅ 支持点击放大预览
✅ 悬停显示交互效果
✅ 全屏查看和下载功能
✅ 响应式设计适配不同屏幕
```

## ✅ 完成功能

- [x] PDF预览功能升级
- [x] 图片点击放大预览
- [x] 图片悬停交互效果
- [x] 图片下载功能
- [x] 预览模态框实现
- [x] 响应式样式设计
- [x] 错误处理和事件监听

## 🎉 使用说明

### 图片预览
1. 鼠标悬停在图片上查看放大图标
2. 点击图片或放大图标打开大图预览
3. 在预览窗口中可以下载图片
4. 点击关闭按钮或遮罩层关闭预览

### PDF预览
1. PDF文件会自动使用vue-office-pdf组件渲染
2. 支持缩放、翻页等PDF查看功能
3. 渲染状态会在控制台显示日志

现在预览页面具备了完整的PDF预览和图片放大预览功能，用户体验大大提升！
